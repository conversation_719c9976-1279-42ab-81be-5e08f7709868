{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/pc-demo/pc-demo.vue?3044", "webpack:///D:/桌面/thinker/app/pages/pc-demo/pc-demo.vue?879c", "webpack:///D:/桌面/thinker/app/pages/pc-demo/pc-demo.vue?6979", "webpack:///D:/桌面/thinker/app/pages/pc-demo/pc-demo.vue?ad1c", "uni-app:///pages/pc-demo/pc-demo.vue", "webpack:///D:/桌面/thinker/app/pages/pc-demo/pc-demo.vue?74c9", "webpack:///D:/桌面/thinker/app/pages/pc-demo/pc-demo.vue?236d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "isPC", "scaleRatio", "windowSize", "width", "height", "demoItems", "icon", "title", "desc", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "updateDeviceInfo", "setupWindowResize", "console", "uni", "duration", "showMessage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2pB,CAAgB,ypBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgE/qB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;QAAAC;QAAAC;MAAA;MACAC,YACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAC;MACA;IAEA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;MACAjB;IACA;EACA;EACAkB;IACAC;MACA;MACA;QACA;QACA;QACA;MACA;;MAEA;MACA;MACA;QACAT;QACAC;MACA;IACA;IAEAS;MAAA;MACA;MACA;QACApB;UACAqB;UACA;YACAX;YACAC;UACA;;UAEA;UACAW;YACAR;YACAD;YACAU;UACA;QACA;MACA;IACA;IAEAC;MACAF;QACAR;QACAD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrJA;AAAA;AAAA;AAAA;AAA0wC,CAAgB,gsCAAG,EAAC,C;;;;;;;;;;;ACA9xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pc-demo/pc-demo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pc-demo/pc-demo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pc-demo.vue?vue&type=template&id=5ca1ce02&scoped=true&\"\nvar renderjs\nimport script from \"./pc-demo.vue?vue&type=script&lang=js&\"\nexport * from \"./pc-demo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pc-demo.vue?vue&type=style&index=0&id=5ca1ce02&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5ca1ce02\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pc-demo/pc-demo.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-demo.vue?vue&type=template&id=5ca1ce02&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-demo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-demo.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"pc-demo\">\n    <!-- 顶部标题 -->\n    <view class=\"header\">\n      <text class=\"title\">PC端适配演示</text>\n      <text class=\"subtitle\">在PC端自动缩放，保持移动端体验</text>\n    </view>\n\n    <!-- 设备状态 -->\n    <view class=\"status-card\">\n      <view class=\"status-item\">\n        <text class=\"status-label\">设备类型：</text>\n        <text class=\"status-value\" :class=\"{ 'pc-device': isPC }\">\n          {{ isPC ? 'PC端' : '移动端' }}\n        </text>\n      </view>\n      <view class=\"status-item\" v-if=\"isPC\">\n        <text class=\"status-label\">缩放比例：</text>\n        <text class=\"status-value\">{{ scaleRatio }}</text>\n      </view>\n      <view class=\"status-item\">\n        <text class=\"status-label\">窗口大小：</text>\n        <text class=\"status-value\">{{ windowSize.width }}x{{ windowSize.height }}</text>\n      </view>\n    </view>\n\n    <!-- 功能演示区域 -->\n    <view class=\"demo-section\">\n      <view class=\"section-title\">功能演示</view>\n      \n      <!-- 卡片列表 -->\n      <view class=\"card-list\">\n        <view class=\"demo-card\" v-for=\"(item, index) in demoItems\" :key=\"index\">\n          <view class=\"card-icon\">{{ item.icon }}</view>\n          <view class=\"card-content\">\n            <text class=\"card-title\">{{ item.title }}</text>\n            <text class=\"card-desc\">{{ item.desc }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 交互演示 -->\n    <view class=\"interaction-section\">\n      <view class=\"section-title\">交互演示</view>\n      \n      <view class=\"button-grid\">\n        <button class=\"demo-btn primary\" @click=\"showMessage('主要按钮')\">主要按钮</button>\n        <button class=\"demo-btn secondary\" @click=\"showMessage('次要按钮')\">次要按钮</button>\n        <button class=\"demo-btn success\" @click=\"showMessage('成功按钮')\">成功按钮</button>\n        <button class=\"demo-btn warning\" @click=\"showMessage('警告按钮')\">警告按钮</button>\n      </view>\n    </view>\n\n    <!-- 底部信息 -->\n    <view class=\"footer\">\n      <text class=\"footer-text\">\n        {{ isPC ? 'PC端已自动缩放适配' : '移动端原生显示' }}\n      </text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'PCDemo',\n  data() {\n    return {\n      isPC: false,\n      scaleRatio: 1.0,\n      windowSize: { width: 0, height: 0 },\n      demoItems: [\n        {\n          icon: '📱',\n          title: '移动端体验',\n          desc: '保持原有的移动端交互体验'\n        },\n        {\n          icon: '💻',\n          title: 'PC端适配',\n          desc: '在PC端自动缩放到合适大小'\n        },\n        {\n          icon: '🎨',\n          title: '视觉一致',\n          desc: '确保在不同设备上的视觉一致性'\n        },\n        {\n          icon: '⚡',\n          title: '性能优化',\n          desc: '轻量级适配，不影响性能'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.updateDeviceInfo()\n    this.setupWindowResize()\n  },\n  beforeDestroy() {\n    // 移除窗口变化监听\n    if (typeof wx !== 'undefined' && wx.offWindowResize) {\n      wx.offWindowResize()\n    }\n  },\n  methods: {\n    updateDeviceInfo() {\n      const app = getApp()\n      if (app.globalData.pcAdapter) {\n        const deviceInfo = app.globalData.pcAdapter.getDeviceInfo()\n        this.isPC = deviceInfo.isPC\n        this.scaleRatio = deviceInfo.scaleRatio\n      }\n\n      // 获取当前窗口大小\n      const systemInfo = uni.getSystemInfoSync()\n      this.windowSize = {\n        width: systemInfo.windowWidth,\n        height: systemInfo.windowHeight\n      }\n    },\n\n    setupWindowResize() {\n      // 监听PC端窗口大小变化\n      if (typeof wx !== 'undefined' && wx.onWindowResize) {\n        wx.onWindowResize((res) => {\n          console.log('窗口大小变化:', res)\n          this.windowSize = {\n            width: res.size.windowWidth,\n            height: res.size.windowHeight\n          }\n\n          // 显示提示\n          uni.showToast({\n            title: `窗口变化: ${res.size.windowWidth}x${res.size.windowHeight}`,\n            icon: 'none',\n            duration: 2000\n          })\n        })\n      }\n    },\n    \n    showMessage(type) {\n      uni.showToast({\n        title: '点击了' + type,\n        icon: 'success'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pc-demo {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);\n  padding: 20px;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30px;\n  \n  .title {\n    display: block;\n    font-size: 24px;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 8px;\n  }\n  \n  .subtitle {\n    display: block;\n    font-size: 14px;\n    color: #666;\n  }\n}\n\n.status-card {\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  \n  .status-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 8px 0;\n    \n    &:not(:last-child) {\n      border-bottom: 1px solid #f0f0f0;\n    }\n  }\n  \n  .status-label {\n    font-size: 14px;\n    color: #666;\n  }\n  \n  .status-value {\n    font-size: 14px;\n    font-weight: bold;\n    color: #333;\n    \n    &.pc-device {\n      color: #52c41a;\n    }\n  }\n}\n\n.demo-section, .interaction-section {\n  margin-bottom: 30px;\n  \n  .section-title {\n    font-size: 18px;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 16px;\n  }\n}\n\n.card-list {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 12px;\n}\n\n.demo-card {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  \n  .card-icon {\n    font-size: 24px;\n    margin-right: 12px;\n  }\n  \n  .card-content {\n    flex: 1;\n    \n    .card-title {\n      display: block;\n      font-size: 14px;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 4px;\n    }\n    \n    .card-desc {\n      display: block;\n      font-size: 12px;\n      color: #666;\n      line-height: 1.4;\n    }\n  }\n}\n\n.button-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 12px;\n}\n\n.demo-btn {\n  padding: 12px 16px;\n  border-radius: 8px;\n  border: none;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &.primary {\n    background: #1890ff;\n    color: white;\n    \n    &:active {\n      background: #096dd9;\n    }\n  }\n  \n  &.secondary {\n    background: #f5f5f5;\n    color: #666;\n    \n    &:active {\n      background: #e6e6e6;\n    }\n  }\n  \n  &.success {\n    background: #52c41a;\n    color: white;\n    \n    &:active {\n      background: #389e0d;\n    }\n  }\n  \n  &.warning {\n    background: #faad14;\n    color: white;\n    \n    &:active {\n      background: #d48806;\n    }\n  }\n}\n\n.footer {\n  text-align: center;\n  margin-top: 40px;\n  \n  .footer-text {\n    font-size: 12px;\n    color: #999;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-demo.vue?vue&type=style&index=0&id=5ca1ce02&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-demo.vue?vue&type=style&index=0&id=5ca1ce02&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753618714794\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}