/**
 * 交互体验优化模块
 * 针对不同平台优化触摸和鼠标交互
 */

import multiPlatformAdapter from './pc-adapter.js'

class InteractionOptimizer {
    constructor() {
        this.isInitialized = false
        this.touchStartTime = 0
        this.touchStartPosition = { x: 0, y: 0 }
        this.longPressTimer = null
        this.longPressDelay = 500 // 长按延迟时间
        
        this.init()
    }

    /**
     * 初始化交互优化
     */
    init() {
        if (this.isInitialized) return
        
        this.setupGlobalStyles()
        this.setupEventOptimization()
        this.setupPlatformSpecificOptimization()
        
        this.isInitialized = true
        console.log('InteractionOptimizer initialized')
    }

    /**
     * 设置全局样式优化
     */
    setupGlobalStyles() {
        // #ifdef H5
        if (typeof document !== 'undefined') {
            const style = document.createElement('style')
            style.id = 'interaction-optimizer-styles'
            style.textContent = this.generateOptimizationCSS()
            document.head.appendChild(style)
        }
        // #endif
    }

    /**
     * 生成优化CSS
     */
    generateOptimizationCSS() {
        return `
            /* 全局交互优化 */
            * {
                -webkit-tap-highlight-color: transparent;
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                user-select: none;
            }
            
            /* 可选择文本 */
            .selectable,
            input,
            textarea,
            [contenteditable] {
                -webkit-user-select: text;
                user-select: text;
            }
            
            /* PC端优化 */
            @media (min-width: 1024px) {
                /* 鼠标悬停效果 */
                .hover-effect {
                    transition: all 0.2s ease;
                }
                
                .hover-effect:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }
                
                /* 按钮悬停 */
                button:hover,
                .button:hover {
                    opacity: 0.8;
                    transform: translateY(-1px);
                }
                
                /* 链接悬停 */
                a:hover,
                .link:hover {
                    text-decoration: underline;
                }
                
                /* 输入框焦点 */
                input:focus,
                textarea:focus {
                    outline: none;
                    border-color: #1890ff;
                    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                }
                
                /* 滚动条样式 */
                ::-webkit-scrollbar {
                    width: 6px;
                    height: 6px;
                }
                
                ::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 3px;
                }
                
                ::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;
                }
                
                ::-webkit-scrollbar-thumb:hover {
                    background: #a8a8a8;
                }
            }
            
            /* 移动端优化 */
            @media (max-width: 1023px) {
                /* 触摸反馈 */
                .touch-feedback:active {
                    background-color: rgba(0, 0, 0, 0.05);
                    transform: scale(0.98);
                }
                
                /* 按钮触摸 */
                button:active,
                .button:active {
                    transform: scale(0.95);
                    opacity: 0.8;
                }
                
                /* 滚动优化 */
                .scroll-container {
                    -webkit-overflow-scrolling: touch;
                    scroll-behavior: smooth;
                }
            }
            
            /* 动画性能优化 */
            .gpu-accelerated {
                transform: translateZ(0);
                will-change: transform;
            }
            
            /* 防抖动画 */
            .no-bounce {
                overscroll-behavior: none;
            }
        `
    }

    /**
     * 设置事件优化
     */
    setupEventOptimization() {
        // #ifdef H5
        if (typeof document !== 'undefined') {
            // 防止双击缩放
            document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true })
            document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true })
            document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false })
            
            // 防止右键菜单
            document.addEventListener('contextmenu', this.handleContextMenu.bind(this))
        }
        // #endif
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(event) {
        this.touchStartTime = Date.now()
        this.touchStartPosition = {
            x: event.touches[0].clientX,
            y: event.touches[0].clientY
        }
        
        // 设置长按定时器
        this.longPressTimer = setTimeout(() => {
            this.handleLongPress(event)
        }, this.longPressDelay)
    }

    /**
     * 处理触摸结束
     */
    handleTouchEnd(event) {
        const touchDuration = Date.now() - this.touchStartTime
        
        // 清除长按定时器
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer)
            this.longPressTimer = null
        }
        
        // 快速点击检测
        if (touchDuration < 200) {
            this.handleQuickTap(event)
        }
    }

    /**
     * 处理触摸移动
     */
    handleTouchMove(event) {
        // 清除长按定时器（移动时取消长按）
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer)
            this.longPressTimer = null
        }
        
        // 防止页面滚动的特殊处理
        const target = event.target
        if (target.classList.contains('no-scroll')) {
            event.preventDefault()
        }
    }

    /**
     * 处理长按
     */
    handleLongPress(event) {
        // 触发自定义长按事件
        const customEvent = new CustomEvent('longpress', {
            detail: {
                originalEvent: event,
                position: this.touchStartPosition
            }
        })
        event.target.dispatchEvent(customEvent)
    }

    /**
     * 处理快速点击
     */
    handleQuickTap(event) {
        // 防止双击缩放
        if (event.detail === 2) {
            event.preventDefault()
        }
    }

    /**
     * 处理右键菜单
     */
    handleContextMenu(event) {
        // 在移动端禁用右键菜单
        if (!multiPlatformAdapter.isPC()) {
            event.preventDefault()
        }
    }

    /**
     * 设置平台特定优化
     */
    setupPlatformSpecificOptimization() {
        const adapterInfo = multiPlatformAdapter.getAdapterInfo()
        
        // PC端优化
        if (adapterInfo.platform.isPC) {
            this.setupPCOptimization()
        }
        
        // 移动端优化
        if (adapterInfo.device.isMobile) {
            this.setupMobileOptimization()
        }
        
        // App端优化
        if (adapterInfo.platform.isApp) {
            this.setupAppOptimization()
        }
    }

    /**
     * PC端优化
     */
    setupPCOptimization() {
        // #ifdef H5
        if (typeof document !== 'undefined') {
            // 键盘导航优化
            document.addEventListener('keydown', this.handleKeyboardNavigation.bind(this))
            
            // 鼠标滚轮优化
            document.addEventListener('wheel', this.handleWheelScroll.bind(this), { passive: false })
        }
        // #endif
    }

    /**
     * 移动端优化
     */
    setupMobileOptimization() {
        // 触摸反馈优化
        // #ifdef H5
        if (typeof document !== 'undefined') {
            document.body.classList.add('mobile-optimized')
        }
        // #endif
    }

    /**
     * App端优化
     */
    setupAppOptimization() {
        // App特定优化
        console.log('App optimization applied')
    }

    /**
     * 键盘导航处理
     */
    handleKeyboardNavigation(event) {
        // Tab键导航优化
        if (event.key === 'Tab') {
            document.body.classList.add('keyboard-navigation')
        }
        
        // Esc键处理
        if (event.key === 'Escape') {
            // 触发全局Esc事件
            const customEvent = new CustomEvent('globalEscape')
            document.dispatchEvent(customEvent)
        }
    }

    /**
     * 滚轮滚动处理
     */
    handleWheelScroll(event) {
        // 平滑滚动优化
        const target = event.target.closest('.smooth-scroll')
        if (target) {
            event.preventDefault()
            target.scrollBy({
                top: event.deltaY,
                behavior: 'smooth'
            })
        }
    }

    /**
     * 添加触摸反馈
     */
    addTouchFeedback(element) {
        if (!element) return
        
        element.classList.add('touch-feedback')
        
        // 添加触摸事件
        element.addEventListener('touchstart', () => {
            element.classList.add('touching')
        }, { passive: true })
        
        element.addEventListener('touchend', () => {
            setTimeout(() => {
                element.classList.remove('touching')
            }, 150)
        }, { passive: true })
    }

    /**
     * 添加悬停效果
     */
    addHoverEffect(element) {
        if (!element || !multiPlatformAdapter.isPC()) return
        
        element.classList.add('hover-effect')
    }

    /**
     * 优化滚动性能
     */
    optimizeScroll(element) {
        if (!element) return
        
        element.classList.add('scroll-container', 'gpu-accelerated')
        
        // 添加滚动优化
        element.style.webkitOverflowScrolling = 'touch'
        element.style.scrollBehavior = 'smooth'
    }

    /**
     * 销毁优化器
     */
    destroy() {
        // #ifdef H5
        if (typeof document !== 'undefined') {
            const style = document.getElementById('interaction-optimizer-styles')
            if (style) {
                style.remove()
            }
        }
        // #endif
        
        this.isInitialized = false
        console.log('InteractionOptimizer destroyed')
    }
}

// 创建全局实例
const interactionOptimizer = new InteractionOptimizer()

export default interactionOptimizer
