/**
 * PC端适配样式
 * 实现小程序在PC端的缩放适配
 */

// PC端适配变量
$pc-scale-ratio: 0.75; // PC端缩放比例
$mobile-width: 375px;   // 移动端基准宽度
$pc-container-width: 420px; // PC端容器宽度 (375 * 0.75 + padding)

// PC端检测混入
@mixin pc-only {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin mobile-only {
  @media (max-width: 1023px) {
    @content;
  }
}

// PC端根容器 - 实现整体缩放
.pc-app-container {
  @include pc-only {
    width: $pc-container-width;
    margin: 0 auto;
    transform-origin: top center;
    transform: scale($pc-scale-ratio);
    background: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    min-height: 100vh;
  }

  @include mobile-only {
    width: 100%;
    margin: 0;
    transform: none;
    box-shadow: none;
    border-radius: 0;
  }
}

// PC端页面背景
.pc-page-background {
  @include pc-only {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 40px 20px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }

  @include mobile-only {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 0;
  }
}

// PC端缩放适配核心样式
.pc-scale-container {
  @include pc-only {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    .pc-app-wrapper {
      width: $mobile-width;
      height: 667px; // iPhone 6/7/8 高度
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
      overflow: hidden;
      transform: scale($pc-scale-ratio);
      transform-origin: center;
      position: relative;
    }
  }

  @include mobile-only {
    width: 100%;
    height: 100vh;

    .pc-app-wrapper {
      width: 100%;
      height: 100%;
      background: #fff;
      border-radius: 0;
      box-shadow: none;
      transform: none;
    }
  }
}

// PC端显示/隐藏工具类
.pc-only {
  @include mobile-only {
    display: none !important;
  }
}

.mobile-only {
  @include pc-only {
    display: none !important;
  }
}

// PC端全局样式重置
@include pc-only {
  body, page {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }

  // 隐藏微信小程序的默认导航栏在PC端的显示
  .uni-page-head {
    display: none !important;
  }

  // 确保页面内容在PC端正确显示
  .uni-page-wrapper {
    height: 100vh !important;
  }
}
