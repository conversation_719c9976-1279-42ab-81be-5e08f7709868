/**
 * PC端响应式样式
 * 提供响应式断点、工具类和PC端优化样式
 */

// 响应式断点定义
$breakpoints: (
  mobile: 0px,
  tablet: 768px,
  desktop: 1024px,
  large: 1440px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $value: map-get($breakpoints, $breakpoint);
    @if $value == 0 {
      @content;
    } @else {
      @media (min-width: $value) {
        @content;
      }
    }
  }
}

// PC端容器样式
.pc-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
  
  @include respond-to(tablet) {
    max-width: 750px;
    padding: 0 24px;
  }
  
  @include respond-to(desktop) {
    max-width: 1000px;
    padding: 0 32px;
  }
  
  @include respond-to(large) {
    max-width: 1200px;
    padding: 0 40px;
  }
}

// PC端卡片样式
.pc-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  @include respond-to(desktop) {
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

// PC端栅格系统
.pc-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
  
  @include respond-to(tablet) {
    margin: 0 -12px;
  }
  
  @include respond-to(desktop) {
    margin: 0 -16px;
  }
}

.pc-col {
  padding: 0 8px;
  
  @include respond-to(tablet) {
    padding: 0 12px;
  }
  
  @include respond-to(desktop) {
    padding: 0 16px;
  }
}

// 列宽度类
@for $i from 1 through 12 {
  .pc-col-#{$i} {
    flex: 0 0 percentage($i / 12);
    max-width: percentage($i / 12);
  }
  
  @include respond-to(tablet) {
    .pc-col-tablet-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  @include respond-to(desktop) {
    .pc-col-desktop-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  @include respond-to(large) {
    .pc-col-large-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
}

// 间距工具类
$spacings: (0, 4, 8, 12, 16, 20, 24, 32, 40, 48, 56, 64);

@each $spacing in $spacings {
  // 内边距
  .pc-p-#{$spacing} { padding: #{$spacing}px !important; }
  .pc-pt-#{$spacing} { padding-top: #{$spacing}px !important; }
  .pc-pr-#{$spacing} { padding-right: #{$spacing}px !important; }
  .pc-pb-#{$spacing} { padding-bottom: #{$spacing}px !important; }
  .pc-pl-#{$spacing} { padding-left: #{$spacing}px !important; }
  .pc-px-#{$spacing} { 
    padding-left: #{$spacing}px !important; 
    padding-right: #{$spacing}px !important; 
  }
  .pc-py-#{$spacing} { 
    padding-top: #{$spacing}px !important; 
    padding-bottom: #{$spacing}px !important; 
  }
  
  // 外边距
  .pc-m-#{$spacing} { margin: #{$spacing}px !important; }
  .pc-mt-#{$spacing} { margin-top: #{$spacing}px !important; }
  .pc-mr-#{$spacing} { margin-right: #{$spacing}px !important; }
  .pc-mb-#{$spacing} { margin-bottom: #{$spacing}px !important; }
  .pc-ml-#{$spacing} { margin-left: #{$spacing}px !important; }
  .pc-mx-#{$spacing} { 
    margin-left: #{$spacing}px !important; 
    margin-right: #{$spacing}px !important; 
  }
  .pc-my-#{$spacing} { 
    margin-top: #{$spacing}px !important; 
    margin-bottom: #{$spacing}px !important; 
  }
}

// 显示/隐藏工具类
.pc-hide-mobile {
  @include respond-to(mobile) {
    display: none !important;
  }
}

.pc-hide-tablet {
  @include respond-to(tablet) {
    display: none !important;
  }
}

.pc-hide-desktop {
  @include respond-to(desktop) {
    display: none !important;
  }
}

.pc-hide-large {
  @include respond-to(large) {
    display: none !important;
  }
}

.pc-show-mobile {
  display: none !important;
  @include respond-to(mobile) {
    display: block !important;
  }
}

.pc-show-tablet {
  display: none !important;
  @include respond-to(tablet) {
    display: block !important;
  }
}

.pc-show-desktop {
  display: none !important;
  @include respond-to(desktop) {
    display: block !important;
  }
}

.pc-show-large {
  display: none !important;
  @include respond-to(large) {
    display: block !important;
  }
}

// 文本对齐
.pc-text-left { text-align: left !important; }
.pc-text-center { text-align: center !important; }
.pc-text-right { text-align: right !important; }

// Flex布局工具类
.pc-flex { display: flex !important; }
.pc-flex-column { flex-direction: column !important; }
.pc-flex-row { flex-direction: row !important; }
.pc-flex-wrap { flex-wrap: wrap !important; }
.pc-flex-nowrap { flex-wrap: nowrap !important; }

.pc-justify-start { justify-content: flex-start !important; }
.pc-justify-center { justify-content: center !important; }
.pc-justify-end { justify-content: flex-end !important; }
.pc-justify-between { justify-content: space-between !important; }
.pc-justify-around { justify-content: space-around !important; }

.pc-align-start { align-items: flex-start !important; }
.pc-align-center { align-items: center !important; }
.pc-align-end { align-items: flex-end !important; }
.pc-align-stretch { align-items: stretch !important; }

// PC端特殊样式
.pc-only {
  display: none;
  
  @include respond-to(desktop) {
    display: block;
  }
}

.mobile-only {
  display: block;
  
  @include respond-to(desktop) {
    display: none;
  }
}

// PC端页面布局
.pc-page {
  min-height: 100vh;
  background: #f5f5f5;
  
  @include respond-to(desktop) {
    padding: 20px;
  }
}

.pc-page-content {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  
  @include respond-to(desktop) {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
}

// PC端导航优化
.pc-nav {
  @include respond-to(desktop) {
    padding: 0 32px;
    height: 60px;
    line-height: 60px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// PC端按钮优化
.pc-button {
  @include respond-to(desktop) {
    min-width: 120px;
    height: 40px;
    border-radius: 6px;
    font-size: 14px;
    
    &:hover {
      opacity: 0.8;
      transform: translateY(-1px);
      transition: all 0.2s ease;
    }
  }
}
