/**
 * 多端适配样式系统
 * 支持PC端缩放、响应式布局和平台特定优化
 */

// ==================== 变量定义 ====================

// PC端缩放配置
$pc-scale-ratio: 0.8;
$pc-container-width: 375px;
$pc-container-height: 667px;

// 响应式断点
$breakpoint-mobile: 0px;
$breakpoint-tablet: 768px;
$breakpoint-desktop: 1024px;
$breakpoint-large: 1440px;

// 平台特定变量
$platform-padding: (
  mobile: 16px,
  tablet: 24px,
  desktop: 32px,
  large: 40px
);

// ==================== 混入定义 ====================

// 响应式断点混入
@mixin respond-to($breakpoint) {
  @if $breakpoint == mobile {
    @media (min-width: #{$breakpoint-mobile}) and (max-width: #{$breakpoint-tablet - 1px}) {
      @content;
    }
  } @else if $breakpoint == tablet {
    @media (min-width: #{$breakpoint-tablet}) and (max-width: #{$breakpoint-desktop - 1px}) {
      @content;
    }
  } @else if $breakpoint == desktop {
    @media (min-width: #{$breakpoint-desktop}) and (max-width: #{$breakpoint-large - 1px}) {
      @content;
    }
  } @else if $breakpoint == large {
    @media (min-width: #{$breakpoint-large}) {
      @content;
    }
  }
}

// PC端检测混入
@mixin pc-only {
  @media (min-width: #{$breakpoint-desktop}) {
    @content;
  }
}

@mixin mobile-only {
  @media (max-width: #{$breakpoint-desktop - 1px}) {
    @content;
  }
}

// 平台特定混入
@mixin platform($platform) {
  .platform-#{$platform} & {
    @content;
  }
}

// ==================== PC端缩放适配 ====================

// PC端全局容器样式
.multi-platform-pc {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  margin: 0;
  padding: 20px;
  transition: all 0.3s ease;

  // 应用容器
  .uni-app,
  #app {
    width: $pc-container-width !important;
    height: $pc-container-height !important;
    max-width: $pc-container-width !important;
    max-height: $pc-container-height !important;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: scale($pc-scale-ratio);
    transform-origin: center;
    position: relative;
    transition: transform 0.3s ease;
  }

  // 页面包装器
  .uni-page-wrapper {
    border-radius: 12px;
    overflow: hidden;
    height: 100% !important;
  }

  // 标签栏适配
  .uni-tabbar {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    border-radius: 0 0 12px 12px;
  }
}

// 移动端恢复正常样式
@include mobile-only {
  .multi-platform-pc {
    background: #f5f5f5;
    padding: 0;
    display: block;

    .uni-app,
    #app {
      width: 100% !important;
      height: 100vh !important;
      max-width: none !important;
      max-height: none !important;
      border-radius: 0;
      box-shadow: none;
      transform: none;
    }

    .uni-page-wrapper {
      border-radius: 0;
    }

    .uni-tabbar {
      position: fixed !important;
    }
  }
}

// ==================== 响应式布局系统 ====================

// 响应式容器
.responsive-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 map-get($platform-padding, mobile);

  @include respond-to(tablet) {
    max-width: 750px;
    padding: 0 map-get($platform-padding, tablet);
  }

  @include respond-to(desktop) {
    max-width: 1000px;
    padding: 0 map-get($platform-padding, desktop);
  }

  @include respond-to(large) {
    max-width: 1200px;
    padding: 0 map-get($platform-padding, large);
  }
}

// 响应式栅格系统
.responsive-grid {
  display: grid;
  gap: 16px;

  // 默认单列
  grid-template-columns: 1fr;

  @include respond-to(tablet) {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  @include respond-to(desktop) {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }

  @include respond-to(large) {
    grid-template-columns: repeat(4, 1fr);
    gap: 28px;
  }
}

// 响应式卡片
.responsive-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  @include respond-to(tablet) {
    padding: 20px;
    border-radius: 10px;
  }

  @include respond-to(desktop) {
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }
  }
}

// ==================== 平台特定样式 ====================

// H5平台样式
.platform-h5 {
  // H5特定的样式优化

  &.platform-h5-pc {
    // H5 PC端特定样式
    .uni-page-head {
      display: none !important; // 隐藏默认导航栏
    }
  }
}

// 微信小程序平台样式
.platform-mp-weixin {
  // 微信小程序特定样式
}

// App平台样式
.platform-app {
  // App特定样式
}

// ==================== 屏幕类型样式 ====================

// 移动端样式
.screen-mobile {
  // 移动端特定样式
}

// 平板端样式
.screen-tablet {
  // 平板端特定样式
}

// 桌面端样式
.screen-desktop {
  // 桌面端特定样式
}

// 大屏样式
.screen-large {
  // 大屏特定样式
}

// ==================== 方向样式 ====================

// 竖屏样式
.orientation-portrait {
  // 竖屏特定样式
}

// 横屏样式
.orientation-landscape {
  // 横屏特定样式
}

// ==================== 工具类 ====================

// 显示/隐藏工具类
.show-mobile {
  display: block;

  @include respond-to(tablet) { display: none; }
  @include respond-to(desktop) { display: none; }
  @include respond-to(large) { display: none; }
}

.show-tablet {
  display: none;

  @include respond-to(tablet) { display: block; }
}

.show-desktop {
  display: none;

  @include respond-to(desktop) { display: block; }
}

.show-large {
  display: none;

  @include respond-to(large) { display: block; }
}

.hide-mobile {
  @include respond-to(mobile) { display: none !important; }
}

.hide-tablet {
  @include respond-to(tablet) { display: none !important; }
}

.hide-desktop {
  @include respond-to(desktop) { display: none !important; }
}

.hide-large {
  @include respond-to(large) { display: none !important; }
}

// PC/移动端显示控制
.pc-only {
  @include mobile-only {
    display: none !important;
  }
}

.mobile-only {
  @include pc-only {
    display: none !important;
  }
}

// 平台显示控制
.platform-show-h5 {
  display: none;
  @include platform(h5) { display: block; }
}

.platform-show-app {
  display: none;
  @include platform(app) { display: block; }
}

.platform-show-mp {
  display: none;
  @include platform(mp-weixin) { display: block; }
  @include platform(mp-alipay) { display: block; }
  @include platform(mp-baidu) { display: block; }
}

// Flex布局工具类
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

// 文本对齐
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

// 间距工具类
$spacings: (0, 4, 8, 12, 16, 20, 24, 32, 40, 48);

@each $spacing in $spacings {
  .m-#{$spacing} { margin: #{$spacing}px; }
  .mt-#{$spacing} { margin-top: #{$spacing}px; }
  .mr-#{$spacing} { margin-right: #{$spacing}px; }
  .mb-#{$spacing} { margin-bottom: #{$spacing}px; }
  .ml-#{$spacing} { margin-left: #{$spacing}px; }
  .mx-#{$spacing} { margin-left: #{$spacing}px; margin-right: #{$spacing}px; }
  .my-#{$spacing} { margin-top: #{$spacing}px; margin-bottom: #{$spacing}px; }

  .p-#{$spacing} { padding: #{$spacing}px; }
  .pt-#{$spacing} { padding-top: #{$spacing}px; }
  .pr-#{$spacing} { padding-right: #{$spacing}px; }
  .pb-#{$spacing} { padding-bottom: #{$spacing}px; }
  .pl-#{$spacing} { padding-left: #{$spacing}px; }
  .px-#{$spacing} { padding-left: #{$spacing}px; padding-right: #{$spacing}px; }
  .py-#{$spacing} { padding-top: #{$spacing}px; padding-bottom: #{$spacing}px; }
}

// ==================== 交互优化 ====================

// PC端交互优化
@include pc-only {
  // 鼠标悬停效果
  .hover-lift {
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .hover-scale {
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  // 按钮优化
  button, .button {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.8;
    }

    &:active {
      transform: scale(0.98);
    }
  }

  // 链接优化
  a, .link {
    cursor: pointer;
    transition: color 0.2s ease;
  }

  // 输入框优化
  input, textarea, .input {
    transition: border-color 0.2s ease, box-shadow 0.2s ease;

    &:focus {
      outline: none;
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }
}

// 触摸设备优化
@include mobile-only {
  // 触摸反馈
  .touch-feedback {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  // 按钮触摸优化
  button, .button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
}

// ==================== 性能优化 ====================

// 硬件加速
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

// 滚动优化
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

// ==================== 全局重置和优化 ====================

// PC端全局样式
@include pc-only {
  body, page {
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  // 隐藏不必要的元素
  .uni-page-head {
    display: none !important;
  }

  // 确保页面内容正确显示
  .uni-page-wrapper {
    height: 100vh !important;
  }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
