{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/test-compile/test-compile.vue?1637", "webpack:///D:/桌面/thinker/app/pages/test-compile/test-compile.vue?ece2", "webpack:///D:/桌面/thinker/app/pages/test-compile/test-compile.vue?14eb", "webpack:///D:/桌面/thinker/app/pages/test-compile/test-compile.vue?9241", "uni-app:///pages/test-compile/test-compile.vue", "webpack:///D:/桌面/thinker/app/pages/test-compile/test-compile.vue?e190", "webpack:///D:/桌面/thinker/app/pages/test-compile/test-compile.vue?27e9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "currentTime", "mounted", "methods", "getTestClass", "getTestString", "updateTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,8pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgCprB;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAA+wC,CAAgB,qsCAAG,EAAC,C;;;;;;;;;;;ACAnyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/test-compile/test-compile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/test-compile/test-compile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./test-compile.vue?vue&type=template&id=2f26eb06&scoped=true&\"\nvar renderjs\nimport script from \"./test-compile.vue?vue&type=script&lang=js&\"\nexport * from \"./test-compile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./test-compile.vue?vue&type=style&index=0&id=2f26eb06&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2f26eb06\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/test-compile/test-compile.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-compile.vue?vue&type=template&id=2f26eb06&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-compile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-compile.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"test-compile\">\n    <view class=\"header\">\n      <text class=\"title\">编译测试页面</text>\n      <text class=\"subtitle\">验证模板字符串修复</text>\n    </view>\n    \n    <view class=\"test-section\">\n      <view class=\"test-item\">\n        <text class=\"label\">动态class测试：</text>\n        <text class=\"value\" :class=\"getTestClass('success')\">测试成功</text>\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"label\">字符串拼接测试：</text>\n        <text class=\"value\">{{ getTestString('Hello', 'World') }}</text>\n      </view>\n      \n      <view class=\"test-item\">\n        <text class=\"label\">编译状态：</text>\n        <text class=\"value success\">✅ 编译成功</text>\n      </view>\n\n      <view class=\"test-item\">\n        <text class=\"label\">当前时间：</text>\n        <text class=\"value\">{{ currentTime }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'TestCompile',\n  data() {\n    return {\n      currentTime: ''\n    }\n  },\n  mounted() {\n    this.updateTime()\n  },\n  methods: {\n    getTestClass(type) {\n      return 'test-' + type\n    },\n\n    getTestString(str1, str2) {\n      return str1 + ' ' + str2 + '!'\n    },\n\n    updateTime() {\n      this.currentTime = new Date().toLocaleTimeString()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.test-compile {\n  padding: 20px;\n  background: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 30px;\n  \n  .title {\n    display: block;\n    font-size: 24px;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 8px;\n  }\n  \n  .subtitle {\n    display: block;\n    font-size: 14px;\n    color: #666;\n  }\n}\n\n.test-section {\n  background: white;\n  border-radius: 12px;\n  padding: 20px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n.test-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.label {\n  font-size: 14px;\n  color: #666;\n}\n\n.value {\n  font-size: 14px;\n  font-weight: bold;\n  color: #333;\n  \n  &.test-success {\n    color: #52c41a;\n  }\n  \n  &.success {\n    color: #52c41a;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-compile.vue?vue&type=style&index=0&id=2f26eb06&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test-compile.vue?vue&type=style&index=0&id=2f26eb06&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753617965409\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}