{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/pc-status/pc-status.vue?2c87", "webpack:///D:/桌面/thinker/app/components/pc-status/pc-status.vue?37b5", "webpack:///D:/桌面/thinker/app/components/pc-status/pc-status.vue?d3a6", "webpack:///D:/桌面/thinker/app/components/pc-status/pc-status.vue?dd1a", "uni-app:///components/pc-status/pc-status.vue", "webpack:///D:/桌面/thinker/app/components/pc-status/pc-status.vue?7ca7", "webpack:///D:/桌面/thinker/app/components/pc-status/pc-status.vue?a8e8"], "names": ["name", "props", "showStatus", "type", "default", "data", "expanded", "isPC", "platformInfo", "deviceInfo", "windowState", "suggestions", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "initStatus", "setupListeners", "uni", "removeListeners", "handleWindowResize", "handlePCStyles", "console", "toggleExpanded", "getWindowModeText"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzBA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4EjrB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;QAEA;QACA;QACA;QAEA;UACA;UACA;QACA;MACA;IACA;IAEAC;MACA;MACAC;MACAA;IACA;IAEAC;MACAD;MACAA;IACA;IAEAE;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEAC;MACAC;IACA;IAEAC;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,ksCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/pc-status/pc-status.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./pc-status.vue?vue&type=template&id=67e17f44&scoped=true&\"\nvar renderjs\nimport script from \"./pc-status.vue?vue&type=script&lang=js&\"\nexport * from \"./pc-status.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pc-status.vue?vue&type=style&index=0&id=67e17f44&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"67e17f44\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/pc-status/pc-status.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-status.vue?vue&type=template&id=67e17f44&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.showStatus && _vm.isPC && _vm.expanded && _vm.windowState\n      ? _vm.getWindowModeText(_vm.windowState.windowMode)\n      : null\n  var g0 =\n    _vm.showStatus && _vm.isPC && _vm.expanded\n      ? _vm.suggestions && _vm.suggestions.suggestions.length > 0\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-status.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-status.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-if=\"showStatus && isPC\" class=\"pc-status-container\">\n    <view class=\"pc-status-card\">\n      <view class=\"status-header\">\n        <text class=\"status-title\">PC端状态</text>\n        <text class=\"status-toggle\" @click=\"toggleExpanded\">\n          {{ expanded ? '收起' : '展开' }}\n        </text>\n      </view>\n      \n      <view v-if=\"expanded\" class=\"status-content\">\n        <!-- 基础信息 -->\n        <view class=\"status-section\">\n          <view class=\"section-title\">基础信息</view>\n          <view class=\"status-item\">\n            <text class=\"label\">平台：</text>\n            <text class=\"value\">{{ platformInfo.current }}</text>\n          </view>\n          <view class=\"status-item\">\n            <text class=\"label\">设备类型：</text>\n            <text class=\"value\">{{ deviceInfo.screenType }}</text>\n          </view>\n          <view class=\"status-item\">\n            <text class=\"label\">PC微信：</text>\n            <text class=\"value\" :class=\"{ 'success': platformInfo.isPCWeixin }\">\n              {{ platformInfo.isPCWeixin ? '是' : '否' }}\n            </text>\n          </view>\n        </view>\n        \n        <!-- 窗口信息 -->\n        <view class=\"status-section\" v-if=\"windowState\">\n          <view class=\"section-title\">窗口信息</view>\n          <view class=\"status-item\">\n            <text class=\"label\">窗口大小：</text>\n            <text class=\"value\">{{ windowState.windowWidth }}x{{ windowState.windowHeight }}</text>\n          </view>\n          <view class=\"status-item\">\n            <text class=\"label\">窗口模式：</text>\n            <text class=\"value\">{{ getWindowModeText(windowState.windowMode) }}</text>\n          </view>\n          <view class=\"status-item\">\n            <text class=\"label\">缩放比例：</text>\n            <text class=\"value\">{{ windowState.ratio }}</text>\n          </view>\n          <view class=\"status-item\">\n            <text class=\"label\">双栏支持：</text>\n            <text class=\"value\" :class=\"{ 'success': windowState.supportsDualColumn }\">\n              {{ windowState.supportsDualColumn ? '支持' : '不支持' }}\n            </text>\n          </view>\n        </view>\n        \n        <!-- 适配建议 -->\n        <view class=\"status-section\" v-if=\"suggestions && suggestions.suggestions.length > 0\">\n          <view class=\"section-title\">适配建议</view>\n          <view class=\"suggestions-list\">\n            <text \n              v-for=\"(suggestion, index) in suggestions.suggestions\" \n              :key=\"index\"\n              class=\"suggestion-item\"\n            >\n              • {{ suggestion }}\n            </text>\n          </view>\n          <view class=\"status-item\">\n            <text class=\"label\">推荐布局：</text>\n            <text class=\"value\">{{ suggestions.recommendedLayout }}</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'PCStatus',\n  props: {\n    showStatus: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      expanded: false,\n      isPC: false,\n      platformInfo: {},\n      deviceInfo: {},\n      windowState: null,\n      suggestions: null\n    }\n  },\n  mounted() {\n    this.initStatus()\n    this.setupListeners()\n  },\n  beforeDestroy() {\n    this.removeListeners()\n  },\n  methods: {\n    initStatus() {\n      const app = getApp()\n      if (app.globalData.multiPlatformAdapter) {\n        const adapter = app.globalData.multiPlatformAdapter\n        const adapterInfo = adapter.getAdapterInfo()\n        \n        this.isPC = adapter.isPC()\n        this.platformInfo = adapterInfo.platform\n        this.deviceInfo = adapterInfo.device\n        \n        if (this.isPC) {\n          this.windowState = adapter.getWindowState()\n          this.suggestions = adapter.getPCAdaptationSuggestions()\n        }\n      }\n    },\n    \n    setupListeners() {\n      // 监听窗口变化\n      uni.$on('windowResize', this.handleWindowResize)\n      uni.$on('applyPCStyles', this.handlePCStyles)\n    },\n    \n    removeListeners() {\n      uni.$off('windowResize', this.handleWindowResize)\n      uni.$off('applyPCStyles', this.handlePCStyles)\n    },\n    \n    handleWindowResize(data) {\n      if (this.isPC) {\n        const app = getApp()\n        const adapter = app.globalData.multiPlatformAdapter\n        this.windowState = adapter.getWindowState()\n        this.suggestions = adapter.getPCAdaptationSuggestions()\n      }\n    },\n    \n    handlePCStyles(data) {\n      console.log('PC状态组件 - PC样式应用:', data)\n    },\n    \n    toggleExpanded() {\n      this.expanded = !this.expanded\n    },\n    \n    getWindowModeText(mode) {\n      const modeMap = {\n        'small': '小窗口',\n        'normal': '正常',\n        'large': '大窗口',\n        'dual-column': '双栏模式'\n      }\n      return modeMap[mode] || mode\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pc-status-container {\n  position: fixed;\n  top: 10px;\n  right: 10px;\n  z-index: 9999;\n  max-width: 300px;\n}\n\n.pc-status-card {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 8px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n.status-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  border-bottom: 1px solid #eee;\n}\n\n.status-title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #333;\n}\n\n.status-toggle {\n  font-size: 12px;\n  color: #007aff;\n  cursor: pointer;\n}\n\n.status-content {\n  padding: 16px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.status-section {\n  margin-bottom: 16px;\n  \n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n\n.section-title {\n  font-size: 13px;\n  font-weight: bold;\n  color: #666;\n  margin-bottom: 8px;\n  padding-bottom: 4px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.status-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 4px 0;\n  font-size: 12px;\n}\n\n.label {\n  color: #666;\n  flex-shrink: 0;\n}\n\n.value {\n  color: #333;\n  font-weight: 500;\n  text-align: right;\n  \n  &.success {\n    color: #52c41a;\n  }\n}\n\n.suggestions-list {\n  margin-bottom: 8px;\n}\n\n.suggestion-item {\n  display: block;\n  font-size: 12px;\n  color: #666;\n  line-height: 1.4;\n  margin-bottom: 4px;\n}\n\n/* 滚动条样式 */\n.status-content::-webkit-scrollbar {\n  width: 4px;\n}\n\n.status-content::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 2px;\n}\n\n.status-content::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 2px;\n}\n\n.status-content::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-status.vue?vue&type=style&index=0&id=67e17f44&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-status.vue?vue&type=style&index=0&id=67e17f44&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753618715030\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}