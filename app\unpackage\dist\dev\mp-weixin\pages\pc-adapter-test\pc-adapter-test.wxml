<view class="multi-platform-test data-v-61c0b3dc"><responsive-container vue-id="53536eac-1" class="data-v-61c0b3dc" bind:__l="__l" vue-slots="{{['default']}}"><responsive-card vue-id="{{('53536eac-2')+','+('53536eac-1')}}" title="多端适配测试" pc-optimized="{{true}}" hoverable="{{true}}" class="data-v-61c0b3dc" bind:__l="__l" vue-slots="{{['default']}}"><view class="info-section data-v-61c0b3dc"><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">当前平台：</text><text class="{{'platform-'+adapterInfo.platform.current}}">{{''+$root.m0+''}}</text></view><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">是否PC端：</text><text class="{{['value','data-v-61c0b3dc',(adapterInfo.platform.isPC)?'success':'']}}">{{''+(adapterInfo.platform.isPC?'是':'否')+''}}</text></view><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">屏幕类型：</text><text class="{{'screen-'+adapterInfo.device.screenType}}">{{''+$root.m1+''}}</text></view><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">设备方向：</text><text class="value data-v-61c0b3dc">{{adapterInfo.device.orientation==='portrait'?'竖屏':'横屏'}}</text></view><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">窗口尺寸：</text><text class="value data-v-61c0b3dc">{{adapterInfo.windowInfo?.windowWidth+" × "+adapterInfo.windowInfo?.windowHeight}}</text></view><block wx:if="{{adapterInfo.platform.isPC}}"><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">PC缩放比例：</text><text class="value data-v-61c0b3dc">{{adapterInfo.config?.pcScale?.ratio||'0.8'}}</text></view></block></view></responsive-card><responsive-card vue-id="{{('53536eac-3')+','+('53536eac-1')}}" title="响应式布局演示" pc-optimized="{{true}}" class="data-v-61c0b3dc" bind:__l="__l" vue-slots="{{['default']}}"><responsive-layout vue-id="{{('53536eac-4')+','+('53536eac-3')}}" type="grid" cols="{{gridCols}}" gap="{{gridGap}}" class="data-v-61c0b3dc" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{demoItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="demo-item data-v-61c0b3dc"><view class="item-icon data-v-61c0b3dc">{{item.icon}}</view><text class="item-title data-v-61c0b3dc">{{item.title}}</text><text class="item-desc data-v-61c0b3dc">{{item.desc}}</text></view></block></responsive-layout></responsive-card><responsive-card vue-id="{{('53536eac-5')+','+('53536eac-1')}}" title="平台特定显示" pc-optimized="{{true}}" class="data-v-61c0b3dc" bind:__l="__l" vue-slots="{{['default']}}"><view class="platform-demos data-v-61c0b3dc"><view class="mobile-only data-v-61c0b3dc"><view class="alert mobile data-v-61c0b3dc">移动端专用内容</view></view><view class="pc-only data-v-61c0b3dc"><view class="alert desktop data-v-61c0b3dc">PC端专用内容（已缩放适配）</view></view><view class="show-mobile data-v-61c0b3dc"><view class="alert info data-v-61c0b3dc">小屏幕显示</view></view><view class="show-tablet data-v-61c0b3dc"><view class="alert warning data-v-61c0b3dc">平板显示</view></view><view class="show-desktop data-v-61c0b3dc"><view class="alert success data-v-61c0b3dc">桌面端显示</view></view><view class="show-large data-v-61c0b3dc"><view class="alert primary data-v-61c0b3dc">大屏显示</view></view></view></responsive-card><responsive-card vue-id="{{('53536eac-6')+','+('53536eac-1')}}" title="交互测试" pc-optimized="{{true}}" class="data-v-61c0b3dc" bind:__l="__l" vue-slots="{{['default']}}"><view class="interaction-demos data-v-61c0b3dc"><view class="button-grid data-v-61c0b3dc"><button data-event-opts="{{[['tap',[['testInteraction',['主要按钮']]]]]}}" class="button primary data-v-61c0b3dc" bindtap="__e">主要按钮</button><button data-event-opts="{{[['tap',[['testInteraction',['次要按钮']]]]]}}" class="button secondary data-v-61c0b3dc" bindtap="__e">次要按钮</button></view><view data-event-opts="{{[['tap',[['testTouch',['$event']]]]]}}" class="touch-demo touch-feedback data-v-61c0b3dc" bindtap="__e"><text class="data-v-61c0b3dc">触摸反馈测试区域</text></view></view></responsive-card><responsive-card vue-id="{{('53536eac-7')+','+('53536eac-1')}}" title="操作控制" pc-optimized="{{true}}" class="data-v-61c0b3dc" bind:__l="__l" vue-slots="{{['default']}}"><view class="control-section data-v-61c0b3dc"><button data-event-opts="{{[['tap',[['refreshInfo',['$event']]]]]}}" class="button primary data-v-61c0b3dc" bindtap="__e">刷新适配信息</button><button data-event-opts="{{[['tap',[['toggleDemo',['$event']]]]]}}" class="button secondary data-v-61c0b3dc" bindtap="__e">切换演示模式</button><block wx:if="{{adapterInfo.platform.isPC}}"><button data-event-opts="{{[['tap',[['adjustScale',['$event']]]]]}}" class="button data-v-61c0b3dc" bindtap="__e">调整PC缩放</button></block></view></responsive-card></responsive-container></view>