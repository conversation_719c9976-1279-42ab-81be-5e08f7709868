<view class="pc-adapter-test data-v-61c0b3dc"><view class="container data-v-61c0b3dc"><view class="card data-v-61c0b3dc"><view class="card-title data-v-61c0b3dc">PC端适配测试</view><view class="info-section data-v-61c0b3dc"><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">是否PC端：</text><text class="{{['value','data-v-61c0b3dc',(deviceInfo.isPC)?'success':'']}}">{{''+(deviceInfo.isPC?'是':'否')+''}}</text></view><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">是否微信PC端：</text><text class="{{['value','data-v-61c0b3dc',(deviceInfo.isPCWeixin)?'success':'']}}">{{''+(deviceInfo.isPCWeixin?'是':'否')+''}}</text></view><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">当前断点：</text><text class="{{'bp-'+deviceInfo.breakpoint}}">{{''+deviceInfo.breakpoint+''}}</text></view><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">窗口尺寸：</text><text class="value data-v-61c0b3dc">{{deviceInfo.windowWidth+" × "+deviceInfo.windowHeight}}</text></view><view class="info-item data-v-61c0b3dc"><text class="label data-v-61c0b3dc">平台：</text><text class="value data-v-61c0b3dc">{{deviceInfo.platform}}</text></view></view></view><view class="card data-v-61c0b3dc"><view class="card-title data-v-61c0b3dc">响应式网格测试</view><view class="{{'grid-'+deviceInfo.breakpoint}}"><block wx:for="{{gridItemCount}}" wx:for-item="n" wx:for-index="__i0__" wx:key="*this"><view class="grid-item data-v-61c0b3dc"><view class="item-content data-v-61c0b3dc">{{n}}</view></view></block></view></view><view class="card data-v-61c0b3dc"><view class="card-title data-v-61c0b3dc">响应式显示测试</view><view class="pc-show-mobile data-v-61c0b3dc"><view class="alert mobile data-v-61c0b3dc">移动端显示</view></view><view class="pc-show-tablet data-v-61c0b3dc"><view class="alert tablet data-v-61c0b3dc">平板端显示</view></view><view class="pc-show-desktop data-v-61c0b3dc"><view class="alert desktop data-v-61c0b3dc">桌面端显示</view></view><view class="pc-show-large data-v-61c0b3dc"><view class="alert large data-v-61c0b3dc">大屏端显示</view></view></view><view class="card data-v-61c0b3dc"><view class="button-group data-v-61c0b3dc"><button data-event-opts="{{[['tap',[['refreshInfo',['$event']]]]]}}" class="btn primary data-v-61c0b3dc" bindtap="__e">刷新信息</button><button data-event-opts="{{[['tap',[['changeGridItems',['$event']]]]]}}" class="btn secondary data-v-61c0b3dc" bindtap="__e">切换网格</button></view></view></view></view>