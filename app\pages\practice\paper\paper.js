let app = getApp();
let that = null;
let config = app.globalData.config;
import {
	post
} from "@/common/js/http.js";
export default {
	data() {
		return {
			title: '试卷中心',
			options: {},
			isLoad: false,
			paperList: [],
			paper_page_set: null
		};
	},
	onLoad(options) {
		that = this;
		that.options = options;
	},
	onShow() {
		// 按顺序执行：先获取页面配置，再获取试卷列表
		that.getPageSet().then(() => {
			that.getList();
		});
	},
	methods: {
		// 获取页面配置信息
		async getPageSet() {
			if (that.paper_page_set) {
				return;
			}
			let res = await post('appConfig/get', { config_type: 'paper_list_page_set' });
			that.paper_page_set = res.data;
		},

		// 获取试卷列表
		async getList() {
			const {
				course_id = 0
			} = that.options || {};
			const {
				category_id = 0
			} = that.options || {};
			let data = {
				course_id: course_id,
				category_id: category_id
			};
			const res = await post('paper/list', data);
			that.paperList = res.data;

			// 如果只有一张试卷，自动调用itemTap方法
			if (that.paperList.length == 1 && that.paper_page_set.single_paper_item_is_navigate_to_practice == 1) {
				that.itemTap(that.paperList[0]);
				return;
			}

			that.isLoad = true;
		},



		itemTap(item) {
			if (item.question_count == 0) {
				app.showToast('试卷暂未上传题目');
				return;
			}

			// 检查是否可以打开试卷
			if (item.is_can_open === false) {
				app.showConfirm('该试卷为VIP专享内容，需要开通课程才能使用，是否立即开通？', () => {
					// 确认后跳转到课程购买页面，使用item.course_id作为参数
					let url = '/pages/practice/course/buy?id=' + item.course_id;
					uni.navigateTo({
						url: url
					});
				}, () => {
					// 取消操作
					app.showToast('您已取消操作');
				});
				return;
			}

			let url = '';

			// 根据配置决定跳转到哪个页面
			if (that.paper_page_set.is_navigate_to_question_type == 1) {
				// 跳转到题型选择页面
				url = `/pages/practice/topic/topic?title=${item.name}&id=${item.course_id}&extend_id=${item.id}&mainType=8&max_page=${item.max_page}&question_type=0`;
			} else {
				// 跳转到练习页面
				url = `../practice?title=${item.name}&id=${item.course_id}&extend_id=${item.id}&mainType=8&max_page=${item.max_page}&question_count=${item.question_count}&question_type=0`;
			}

			console.log(url);
			uni.navigateTo({
				url: url
			});
		}
	}
};