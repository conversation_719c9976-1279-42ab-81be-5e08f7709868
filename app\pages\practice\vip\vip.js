import {
	get
} from "@/common/js/http.js";

let app = getApp(),
	that = null,
	cache = app.globalData.config.storage;

export default {
	data() {
		return {
			isLoad: false,
			keyword: '',
			videoList: [],
			appIsAudit: false,
			isLoading: false,
			hasMore: true,
			page: 1,
			pageSize: 10,
			timer: null,
			course_id: 0
		}
	},
	onLoad(options) {
		app.globalData.showShareMenu();
		that = this;
		that.course_id = options.course_id || 0;
	},
	onShow() {
		that.appIsAudit = app.globalData.checkAppIsAudit();
		if (that.appIsAudit) {
			uni.reLaunch({
				url: '/pages/index/index'
			});
		}
		that.getList();
	},
	onReachBottom() {
		if (that.hasMore && !that.isLoading) {
			that.page++;
			that.getList();
		}
	},
	onShareAppMessage() {
		let config = app.globalData.getShareConfig();
		config.title = '在线网课';
		config.path = '/pages/practice/vip/vip';
		return config;
	},
	methods: {
		async getList() {
			if (!that.hasMore) {
				return
			}
			that.isLoading = true;
			var res = await get('videoCollection/getList', {
				page: that.page,
				pageSize: that.pageSize,
				keyword: that.keyword,
				course_id: that.course_id
			})

			// 如果是第一页，直接赋值；如果是加载更多，则追加数据
			if (that.page === 1) {
				that.videoList = res.data || [];
			} else {
				that.videoList = [...that.videoList, ...(res.data || [])];
			}

			that.isLoad = true;
			that.isLoading = false; // 重置加载状态
			if (res.data.length < that.pageSize) {
				that.hasMore = false;
			}
		},

		// 搜索输入变化
		onInputChange(e) {

		},

		// 搜索点击
		searchTap() {
			// 重置页码并执行搜索
			that.page = 1;
			that.videoList = [];
			that.hasMore = true;
			that.getList();
		},

		// 视频点击
		videoTap(e) {
			const index = e.currentTarget.dataset.index;
			const video = this.videoList[index];
			console.log('点击视频:', video);
			// 跳转到视频详情页
			uni.navigateTo({
				url: '/pages/practice/vip/detail?id=' + video.id
			});
		},
	}
}; 