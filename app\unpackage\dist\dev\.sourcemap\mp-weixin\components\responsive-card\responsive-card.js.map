{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?6c65", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?6dc9", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?24fd", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?3d58", "uni-app:///components/responsive-card/responsive-card.vue", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?0eaa", "webpack:///D:/桌面/thinker/app/components/responsive-card/responsive-card.vue?a440"], "names": ["name", "props", "title", "type", "default", "subtitle", "padding", "mobile", "tablet", "desktop", "large", "margin", "radius", "shadow", "background", "border", "clickable", "hoverable", "pcOptimized", "data", "adapterInfo", "platform", "device", "screenType", "computed", "cardClasses", "classes", "cardStyles", "styles", "Object", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "updateAdapterInfo", "setupEventListeners", "multiPlatformAdapter", "removeEventListeners", "getResponsiveValue", "formatValue", "handleClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACuBvrB;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IAEA;IACAC;MACAF;MACAC;IACA;IAEA;IACAE;MACAH;MACAC;QAAA;UACAG;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAC;MACAR;MACAC;IACA;IAEA;IACAQ;MACAT;MACAC;QAAA;UACAG;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAG;MACAV;MACAC;QAAA;UACAG;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAI;MACAX;MACAC;IACA;IAEA;IACAW;MACAZ;MACAC;IACA;IAEA;IACAY;MACAb;MACAC;IACA;IAEA;IACAa;MACAd;MACAC;IACA;IAEA;IACAc;MACAf;MACAC;IACA;EACA;EAEAe;IACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACAC;MACA;;MAEA;MACAC;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MAEA;QACAA;MACA;MAEA;QACAA;MACA;;MAEA;MACA;MACA;QACA;UACAA;QACA;UACAA;QACA;MACA;MAEA;IACA;IAEAC;MACA;;MAEA;MACA;MACA;QACAC;MACA;;MAEA;MACA;MACA;QACAA;MACA;;MAEA;MACA;MACA;QACAA;MACA;;MAEA;MACA;MACA;QACA;UACAA;QACA;UACAC;QACA;MACA;;MAEA;MACA;MACA;QACAD;MACA;MAEA;IACA;EACA;EAEAE;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IAEAC;MAAA;MACA;QACA;MACA;MAEAC;IACA;IAEAC;MACA;QACAD;MACA;IACA;IAEAE;MACA;IACA;IAEAC;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACpPA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,wsCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/responsive-card/responsive-card.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./responsive-card.vue?vue&type=template&id=ab6b2dd0&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-card.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-card.vue?vue&type=style&index=0&id=ab6b2dd0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ab6b2dd0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/responsive-card/responsive-card.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=template&id=ab6b2dd0&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"responsive-card\" :class=\"cardClasses\" :style=\"cardStyles\" @click=\"handleClick\">\n    <!-- 卡片头部 -->\n    <view v-if=\"$slots.header || title\" class=\"responsive-card__header\">\n      <slot name=\"header\">\n        <text class=\"responsive-card__title\">{{ title }}</text>\n        <text v-if=\"subtitle\" class=\"responsive-card__subtitle\">{{ subtitle }}</text>\n      </slot>\n    </view>\n    \n    <!-- 卡片内容 -->\n    <view class=\"responsive-card__body\">\n      <slot></slot>\n    </view>\n    \n    <!-- 卡片底部 -->\n    <view v-if=\"$slots.footer\" class=\"responsive-card__footer\">\n      <slot name=\"footer\"></slot>\n    </view>\n  </view>\n</template>\n\n<script>\nimport multiPlatformAdapter from '@/common/js/pc-adapter.js'\n\nexport default {\n  name: 'ResponsiveCard',\n  props: {\n    // 卡片标题\n    title: {\n      type: String,\n      default: ''\n    },\n    \n    // 卡片副标题\n    subtitle: {\n      type: String,\n      default: ''\n    },\n    \n    // 内边距配置\n    padding: {\n      type: [String, Number, Object],\n      default: () => ({\n        mobile: '16px',\n        tablet: '20px',\n        desktop: '24px',\n        large: '28px'\n      })\n    },\n    \n    // 外边距配置\n    margin: {\n      type: [String, Number, Object],\n      default: null\n    },\n    \n    // 边框圆角配置\n    radius: {\n      type: [String, Number, Object],\n      default: () => ({\n        mobile: '8px',\n        tablet: '10px',\n        desktop: '12px',\n        large: '14px'\n      })\n    },\n    \n    // 阴影配置\n    shadow: {\n      type: [Boolean, String, Object],\n      default: () => ({\n        mobile: 'sm',\n        tablet: 'md',\n        desktop: 'lg',\n        large: 'xl'\n      })\n    },\n    \n    // 背景配置\n    background: {\n      type: [String, Object],\n      default: '#fff'\n    },\n    \n    // 边框配置\n    border: {\n      type: [String, Object],\n      default: null\n    },\n    \n    // 是否可点击\n    clickable: {\n      type: Boolean,\n      default: false\n    },\n    \n    // 是否启用悬停效果\n    hoverable: {\n      type: Boolean,\n      default: true\n    },\n    \n    // PC端优化\n    pcOptimized: {\n      type: Boolean,\n      default: true\n    }\n  },\n  \n  data() {\n    return {\n      adapterInfo: {\n        platform: {},\n        device: {},\n        screenType: 'mobile'\n      }\n    }\n  },\n  \n  computed: {\n    cardClasses() {\n      const classes = ['responsive-card']\n      \n      // 屏幕类型类\n      classes.push(`responsive-card--${this.adapterInfo.device.screenType}`)\n      \n      // 平台类\n      if (this.adapterInfo.platform.current) {\n        classes.push(`responsive-card--platform-${this.adapterInfo.platform.current}`)\n      }\n      \n      // 功能类\n      if (this.clickable) {\n        classes.push('responsive-card--clickable')\n      }\n      \n      if (this.hoverable && this.adapterInfo.platform.isPC) {\n        classes.push('responsive-card--hoverable')\n      }\n      \n      if (this.pcOptimized && this.adapterInfo.platform.isPC) {\n        classes.push('responsive-card--pc-optimized')\n      }\n      \n      // 阴影类\n      const shadowValue = this.getResponsiveValue(this.shadow)\n      if (shadowValue) {\n        if (typeof shadowValue === 'boolean' && shadowValue) {\n          classes.push('responsive-card--shadow')\n        } else if (typeof shadowValue === 'string') {\n          classes.push(`responsive-card--shadow-${shadowValue}`)\n        }\n      }\n      \n      return classes\n    },\n    \n    cardStyles() {\n      const styles = {}\n      \n      // 内边距\n      const padding = this.getResponsiveValue(this.padding)\n      if (padding) {\n        styles.padding = this.formatValue(padding)\n      }\n      \n      // 外边距\n      const margin = this.getResponsiveValue(this.margin)\n      if (margin) {\n        styles.margin = this.formatValue(margin)\n      }\n      \n      // 边框圆角\n      const radius = this.getResponsiveValue(this.radius)\n      if (radius) {\n        styles.borderRadius = this.formatValue(radius)\n      }\n      \n      // 背景\n      const background = this.getResponsiveValue(this.background)\n      if (background) {\n        if (typeof background === 'string') {\n          styles.backgroundColor = background\n        } else if (typeof background === 'object') {\n          Object.assign(styles, background)\n        }\n      }\n      \n      // 边框\n      const border = this.getResponsiveValue(this.border)\n      if (border) {\n        styles.border = border\n      }\n      \n      return styles\n    }\n  },\n  \n  mounted() {\n    this.updateAdapterInfo()\n    this.setupEventListeners()\n  },\n  \n  beforeDestroy() {\n    this.removeEventListeners()\n  },\n  \n  methods: {\n    updateAdapterInfo() {\n      this.adapterInfo = multiPlatformAdapter.getAdapterInfo()\n    },\n    \n    setupEventListeners() {\n      this.resizeHandler = () => {\n        this.updateAdapterInfo()\n      }\n      \n      multiPlatformAdapter.on('resize', this.resizeHandler)\n    },\n    \n    removeEventListeners() {\n      if (this.resizeHandler) {\n        multiPlatformAdapter.off('resize', this.resizeHandler)\n      }\n    },\n    \n    getResponsiveValue(value) {\n      return multiPlatformAdapter.getResponsiveValue(value)\n    },\n    \n    formatValue(value) {\n      if (typeof value === 'number') {\n        return `${value}px`\n      }\n      return value\n    },\n    \n    handleClick(event) {\n      if (this.clickable) {\n        this.$emit('click', event)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/common/css/pc-responsive.scss';\n\n.responsive-card {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  transition: all 0.3s ease;\n  position: relative;\n  \n  &__header {\n    padding: 16px 16px 0;\n    border-bottom: 1px solid #f0f0f0;\n    margin-bottom: 16px;\n  }\n  \n  &__title {\n    display: block;\n    font-size: 16px;\n    font-weight: 600;\n    color: #333;\n    margin-bottom: 4px;\n  }\n  \n  &__subtitle {\n    display: block;\n    font-size: 14px;\n    color: #666;\n  }\n  \n  &__body {\n    padding: 16px;\n  }\n  \n  &__footer {\n    padding: 0 16px 16px;\n    border-top: 1px solid #f0f0f0;\n    margin-top: 16px;\n  }\n  \n  // 可点击样式\n  &--clickable {\n    cursor: pointer;\n    \n    &:active {\n      transform: scale(0.98);\n    }\n  }\n  \n  // 悬停效果\n  &--hoverable {\n    @include pc-only {\n      &:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n      }\n    }\n  }\n  \n  // PC端优化\n  &--pc-optimized {\n    @include pc-only {\n      border-radius: 12px;\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n    }\n  }\n  \n  // 阴影样式\n  &--shadow-sm {\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);\n  }\n  \n  &--shadow-md {\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  }\n  \n  &--shadow-lg {\n    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\n  }\n  \n  &--shadow-xl {\n    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\n  }\n  \n  // 响应式样式\n  &--mobile {\n    @include respond-to(mobile) {\n      border-radius: 8px;\n      \n      .responsive-card__header,\n      .responsive-card__body,\n      .responsive-card__footer {\n        padding-left: 16px;\n        padding-right: 16px;\n      }\n    }\n  }\n  \n  &--tablet {\n    @include respond-to(tablet) {\n      border-radius: 10px;\n      \n      .responsive-card__header,\n      .responsive-card__body,\n      .responsive-card__footer {\n        padding-left: 20px;\n        padding-right: 20px;\n      }\n    }\n  }\n  \n  &--desktop {\n    @include respond-to(desktop) {\n      border-radius: 12px;\n      \n      .responsive-card__header,\n      .responsive-card__body,\n      .responsive-card__footer {\n        padding-left: 24px;\n        padding-right: 24px;\n      }\n    }\n  }\n  \n  &--large {\n    @include respond-to(large) {\n      border-radius: 14px;\n      \n      .responsive-card__header,\n      .responsive-card__body,\n      .responsive-card__footer {\n        padding-left: 28px;\n        padding-right: 28px;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=style&index=0&id=ab6b2dd0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-card.vue?vue&type=style&index=0&id=ab6b2dd0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753616752098\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}