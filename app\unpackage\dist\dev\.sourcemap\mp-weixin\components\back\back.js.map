{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/back/back.vue?a5bb", "webpack:///D:/桌面/thinker/app/components/back/back.vue?20b3", "webpack:///D:/桌面/thinker/app/components/back/back.vue?9781", "webpack:///D:/桌面/thinker/app/components/back/back.vue?39d0", "uni-app:///components/back/back.vue", "webpack:///D:/桌面/thinker/app/components/back/back.vue?db52", "webpack:///D:/桌面/thinker/app/components/back/back.vue?ed2d"], "names": ["name", "data", "StatusBar", "CustomBar", "props", "showBack", "type", "default", "showHome", "showBackLeft", "showBackIcon", "showHomeIcon", "showBackText", "backText", "customClass", "homeUrl", "showTitle", "title", "useDefaultBack", "computed", "customStyle", "methods", "BackPage", "uni", "delta", "HomePage", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwpB,CAAgB,spBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuB5qB;EACAA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;;MAEA;MACA;QACAC;UACAC;QACA;MACA;IACA;IACAC;MACAF;QACAG;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1GA;AAAA;AAAA;AAAA;AAAi8B,CAAgB,25BAAG,EAAC,C;;;;;;;;;;;ACAr9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/back/back.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./back.vue?vue&type=template&id=02abcee4&\"\nvar renderjs\nimport script from \"./back.vue?vue&type=script&lang=js&\"\nexport * from \"./back.vue?vue&type=script&lang=js&\"\nimport style0 from \"./back.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/back/back.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./back.vue?vue&type=template&id=02abcee4&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./back.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./back.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"back-container\" :class=\"[customClass]\" :style=\"customStyle\">\r\n    <view class=\"back-bar\">\r\n      <view class=\"left-actions\">\r\n        <view class=\"action-wrapper\" v-if=\"showBackLeft\">\r\n          <view class=\"left-action\" @tap=\"BackPage\" v-if=\"showBack\">\r\n            <text class=\"cuIcon-back\" v-if=\"showBackIcon\"></text>\r\n            <text class=\"back-text\" v-if=\"showBackText\">{{backText}}</text>\r\n          </view>\r\n          <view class=\"divider-line\" v-if=\"showBack && showHome\"></view>\r\n          <view class=\"home-action\" @tap=\"HomePage\" v-if=\"showHome\">\r\n            <text class=\"cuIcon-home\" v-if=\"showHomeIcon\"></text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view class=\"content text-white\" v-if=\"showTitle\">\r\n        <text>{{title}}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'back',\r\n  data() {\r\n    return {\r\n      StatusBar: this.StatusBar,\r\n      CustomBar: this.CustomBar\r\n    }\r\n  },\r\n  props: {\r\n    showBack: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showHome: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showBackLeft: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showBackIcon: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showHomeIcon: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showBackText: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    backText: {\r\n      type: String,\r\n      default: '返回'\r\n    },\r\n    customClass: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    homeUrl: {\r\n      type: String,\r\n      default: '/pages/index/index'\r\n    },\r\n    showTitle: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: '标题'\r\n    },\r\n    useDefaultBack: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  computed: {\r\n    customStyle() {\r\n      var StatusBar = this.StatusBar;\r\n      var style = `padding-top:${StatusBar}px;`;\r\n      return style;\r\n    }\r\n  },\r\n  methods: {\r\n    BackPage() {\r\n      // 先触发自定义事件，让父组件有机会执行自己的逻辑\r\n      this.$emit('beforeBack');\r\n      \r\n      // 如果设置了使用默认返回行为，则执行默认的返回逻辑\r\n      if (this.useDefaultBack) {\r\n        uni.navigateBack({\r\n          delta: 1\r\n        });\r\n      }\r\n    },\r\n    HomePage() {\r\n      uni.reLaunch({\r\n        url: this.homeUrl\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n\r\n.back-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  min-height: 90rpx;\r\n  padding: 0 10rpx;\r\n  position: relative;\r\n}\r\n\r\n.left-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 100%;\r\n}\r\n\r\n.action-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: rgba(0, 0, 0, 0.1);\r\n  border-radius: 30rpx;\r\n  padding: 8rpx 20rpx;\r\n}\r\n\r\n.left-action {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 100%;\r\n}\r\n\r\n.home-action {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 100%;\r\n  margin-left: 0;\r\n}\r\n\r\n.divider-line {\r\n  height: 28rpx;\r\n  width: 2rpx;\r\n  background-color: rgba(255, 255, 255, 0.7);\r\n  margin: 0 15rpx;\r\n}\r\n\r\n.back-text {\r\n  font-size: 28rpx;\r\n  margin-left: 10rpx;\r\n  color: #ffffff;\r\n}\r\n\r\n.cuIcon-back, .cuIcon-home {\r\n  font-size: 36rpx;\r\n  color: #ffffff;\r\n}\r\n\r\n.content {\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  pointer-events: none;\r\n  z-index: 1;\r\n  width: 60%;\r\n  margin: 0 auto;\r\n}\r\n\r\n.content text {\r\n  font-size: 32rpx;\r\n  font-weight: 500;\r\n  color: #ffffff;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 100%;\r\n  display: block;\r\n}\r\n</style>", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./back.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./back.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753616751035\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}