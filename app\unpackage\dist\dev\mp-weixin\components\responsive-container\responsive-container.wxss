@charset "UTF-8";
/**
 * uni-app全局样式 - 多端适配版本
 */
/**
 * 多端适配样式系统
 * 支持PC端缩放、响应式布局和平台特定优化
 */
.multi-platform-pc.data-v-65a324f8 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  margin: 0;
  padding: 20px;
  transition: all 0.3s ease;
}
.multi-platform-pc .uni-app.data-v-65a324f8,
.multi-platform-pc #app.data-v-65a324f8 {
  width: 375px !important;
  height: 667px !important;
  max-width: 375px !important;
  max-height: 667px !important;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  -webkit-transform-origin: center;
          transform-origin: center;
  position: relative;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.multi-platform-pc .uni-page-wrapper.data-v-65a324f8 {
  border-radius: 12px;
  overflow: hidden;
  height: 100% !important;
}
.multi-platform-pc .uni-tabbar.data-v-65a324f8 {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  border-radius: 0 0 12px 12px;
}
@media (max-width: 1023px) {
.multi-platform-pc.data-v-65a324f8 {
    background: #f5f5f5;
    padding: 0;
    display: block;
}
.multi-platform-pc .uni-app.data-v-65a324f8,
  .multi-platform-pc #app.data-v-65a324f8 {
    width: 100% !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0;
    box-shadow: none;
    -webkit-transform: none;
            transform: none;
}
.multi-platform-pc .uni-page-wrapper.data-v-65a324f8 {
    border-radius: 0;
}
.multi-platform-pc .uni-tabbar.data-v-65a324f8 {
    position: fixed !important;
}
}
.responsive-container.data-v-65a324f8, .container.data-v-65a324f8 {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-container.data-v-65a324f8, .container.data-v-65a324f8 {
    max-width: 750px;
    padding: 0 24px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-container.data-v-65a324f8, .container.data-v-65a324f8 {
    max-width: 1000px;
    padding: 0 32px;
}
}
@media (min-width: 1440px) {
.responsive-container.data-v-65a324f8, .container.data-v-65a324f8 {
    max-width: 1200px;
    padding: 0 40px;
}
}
.responsive-grid.data-v-65a324f8 {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-grid.data-v-65a324f8 {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-grid.data-v-65a324f8 {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
}
}
@media (min-width: 1440px) {
.responsive-grid.data-v-65a324f8 {
    grid-template-columns: repeat(4, 1fr);
    gap: 28px;
}
}
.responsive-card.data-v-65a324f8, .card.data-v-65a324f8 {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-card.data-v-65a324f8, .card.data-v-65a324f8 {
    padding: 20px;
    border-radius: 10px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-card.data-v-65a324f8, .card.data-v-65a324f8 {
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
.responsive-card.data-v-65a324f8:hover, .card.data-v-65a324f8:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}
}
.platform-h5.platform-h5-pc .uni-page-head.data-v-65a324f8 {
  display: none !important;
}
.show-mobile.data-v-65a324f8 {
  display: block;
}
@media (min-width: 768px) and (max-width: 1023px) {
.show-mobile.data-v-65a324f8 {
    display: none;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.show-mobile.data-v-65a324f8 {
    display: none;
}
}
@media (min-width: 1440px) {
.show-mobile.data-v-65a324f8 {
    display: none;
}
}
.show-tablet.data-v-65a324f8 {
  display: none;
}
@media (min-width: 768px) and (max-width: 1023px) {
.show-tablet.data-v-65a324f8 {
    display: block;
}
}
.show-desktop.data-v-65a324f8 {
  display: none;
}
@media (min-width: 1024px) and (max-width: 1439px) {
.show-desktop.data-v-65a324f8 {
    display: block;
}
}
.show-large.data-v-65a324f8 {
  display: none;
}
@media (min-width: 1440px) {
.show-large.data-v-65a324f8 {
    display: block;
}
}
@media (min-width: 0px) and (max-width: 767px) {
.hide-mobile.data-v-65a324f8 {
    display: none !important;
}
}
@media (min-width: 768px) and (max-width: 1023px) {
.hide-tablet.data-v-65a324f8 {
    display: none !important;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.hide-desktop.data-v-65a324f8 {
    display: none !important;
}
}
@media (min-width: 1440px) {
.hide-large.data-v-65a324f8 {
    display: none !important;
}
}
@media (max-width: 1023px) {
.pc-only.data-v-65a324f8 {
    display: none !important;
}
}
@media (min-width: 1024px) {
.mobile-only.data-v-65a324f8 {
    display: none !important;
}
}
.platform-show-h5.data-v-65a324f8 {
  display: none;
}
.platform-h5 .platform-show-h5.data-v-65a324f8 {
  display: block;
}
.platform-show-app.data-v-65a324f8 {
  display: none;
}
.platform-app .platform-show-app.data-v-65a324f8 {
  display: block;
}
.platform-show-mp.data-v-65a324f8 {
  display: none;
}
.platform-mp-weixin .platform-show-mp.data-v-65a324f8 {
  display: block;
}
.platform-mp-alipay .platform-show-mp.data-v-65a324f8 {
  display: block;
}
.platform-mp-baidu .platform-show-mp.data-v-65a324f8 {
  display: block;
}
.flex.data-v-65a324f8 {
  display: flex;
}
.flex-column.data-v-65a324f8 {
  flex-direction: column;
}
.flex-row.data-v-65a324f8 {
  flex-direction: row;
}
.flex-wrap.data-v-65a324f8 {
  flex-wrap: wrap;
}
.justify-center.data-v-65a324f8 {
  justify-content: center;
}
.justify-between.data-v-65a324f8 {
  justify-content: space-between;
}
.justify-around.data-v-65a324f8 {
  justify-content: space-around;
}
.align-center.data-v-65a324f8 {
  align-items: center;
}
.align-start.data-v-65a324f8 {
  align-items: flex-start;
}
.align-end.data-v-65a324f8 {
  align-items: flex-end;
}
.text-left.data-v-65a324f8 {
  text-align: left;
}
.text-center.data-v-65a324f8 {
  text-align: center;
}
.text-right.data-v-65a324f8 {
  text-align: right;
}
.m-0.data-v-65a324f8 {
  margin: 0px;
}
.mt-0.data-v-65a324f8 {
  margin-top: 0px;
}
.mr-0.data-v-65a324f8 {
  margin-right: 0px;
}
.mb-0.data-v-65a324f8 {
  margin-bottom: 0px;
}
.ml-0.data-v-65a324f8 {
  margin-left: 0px;
}
.mx-0.data-v-65a324f8 {
  margin-left: 0px;
  margin-right: 0px;
}
.my-0.data-v-65a324f8 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.p-0.data-v-65a324f8 {
  padding: 0px;
}
.pt-0.data-v-65a324f8 {
  padding-top: 0px;
}
.pr-0.data-v-65a324f8 {
  padding-right: 0px;
}
.pb-0.data-v-65a324f8 {
  padding-bottom: 0px;
}
.pl-0.data-v-65a324f8 {
  padding-left: 0px;
}
.px-0.data-v-65a324f8 {
  padding-left: 0px;
  padding-right: 0px;
}
.py-0.data-v-65a324f8 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.m-4.data-v-65a324f8 {
  margin: 4px;
}
.mt-4.data-v-65a324f8 {
  margin-top: 4px;
}
.mr-4.data-v-65a324f8 {
  margin-right: 4px;
}
.mb-4.data-v-65a324f8 {
  margin-bottom: 4px;
}
.ml-4.data-v-65a324f8 {
  margin-left: 4px;
}
.mx-4.data-v-65a324f8 {
  margin-left: 4px;
  margin-right: 4px;
}
.my-4.data-v-65a324f8 {
  margin-top: 4px;
  margin-bottom: 4px;
}
.p-4.data-v-65a324f8 {
  padding: 4px;
}
.pt-4.data-v-65a324f8 {
  padding-top: 4px;
}
.pr-4.data-v-65a324f8 {
  padding-right: 4px;
}
.pb-4.data-v-65a324f8 {
  padding-bottom: 4px;
}
.pl-4.data-v-65a324f8 {
  padding-left: 4px;
}
.px-4.data-v-65a324f8 {
  padding-left: 4px;
  padding-right: 4px;
}
.py-4.data-v-65a324f8 {
  padding-top: 4px;
  padding-bottom: 4px;
}
.m-8.data-v-65a324f8 {
  margin: 8px;
}
.mt-8.data-v-65a324f8 {
  margin-top: 8px;
}
.mr-8.data-v-65a324f8 {
  margin-right: 8px;
}
.mb-8.data-v-65a324f8 {
  margin-bottom: 8px;
}
.ml-8.data-v-65a324f8 {
  margin-left: 8px;
}
.mx-8.data-v-65a324f8 {
  margin-left: 8px;
  margin-right: 8px;
}
.my-8.data-v-65a324f8 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.p-8.data-v-65a324f8 {
  padding: 8px;
}
.pt-8.data-v-65a324f8 {
  padding-top: 8px;
}
.pr-8.data-v-65a324f8 {
  padding-right: 8px;
}
.pb-8.data-v-65a324f8 {
  padding-bottom: 8px;
}
.pl-8.data-v-65a324f8 {
  padding-left: 8px;
}
.px-8.data-v-65a324f8 {
  padding-left: 8px;
  padding-right: 8px;
}
.py-8.data-v-65a324f8 {
  padding-top: 8px;
  padding-bottom: 8px;
}
.m-12.data-v-65a324f8 {
  margin: 12px;
}
.mt-12.data-v-65a324f8 {
  margin-top: 12px;
}
.mr-12.data-v-65a324f8 {
  margin-right: 12px;
}
.mb-12.data-v-65a324f8 {
  margin-bottom: 12px;
}
.ml-12.data-v-65a324f8 {
  margin-left: 12px;
}
.mx-12.data-v-65a324f8 {
  margin-left: 12px;
  margin-right: 12px;
}
.my-12.data-v-65a324f8 {
  margin-top: 12px;
  margin-bottom: 12px;
}
.p-12.data-v-65a324f8 {
  padding: 12px;
}
.pt-12.data-v-65a324f8 {
  padding-top: 12px;
}
.pr-12.data-v-65a324f8 {
  padding-right: 12px;
}
.pb-12.data-v-65a324f8 {
  padding-bottom: 12px;
}
.pl-12.data-v-65a324f8 {
  padding-left: 12px;
}
.px-12.data-v-65a324f8 {
  padding-left: 12px;
  padding-right: 12px;
}
.py-12.data-v-65a324f8 {
  padding-top: 12px;
  padding-bottom: 12px;
}
.m-16.data-v-65a324f8 {
  margin: 16px;
}
.mt-16.data-v-65a324f8 {
  margin-top: 16px;
}
.mr-16.data-v-65a324f8 {
  margin-right: 16px;
}
.mb-16.data-v-65a324f8 {
  margin-bottom: 16px;
}
.ml-16.data-v-65a324f8 {
  margin-left: 16px;
}
.mx-16.data-v-65a324f8 {
  margin-left: 16px;
  margin-right: 16px;
}
.my-16.data-v-65a324f8 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.p-16.data-v-65a324f8 {
  padding: 16px;
}
.pt-16.data-v-65a324f8 {
  padding-top: 16px;
}
.pr-16.data-v-65a324f8 {
  padding-right: 16px;
}
.pb-16.data-v-65a324f8 {
  padding-bottom: 16px;
}
.pl-16.data-v-65a324f8 {
  padding-left: 16px;
}
.px-16.data-v-65a324f8 {
  padding-left: 16px;
  padding-right: 16px;
}
.py-16.data-v-65a324f8 {
  padding-top: 16px;
  padding-bottom: 16px;
}
.m-20.data-v-65a324f8 {
  margin: 20px;
}
.mt-20.data-v-65a324f8 {
  margin-top: 20px;
}
.mr-20.data-v-65a324f8 {
  margin-right: 20px;
}
.mb-20.data-v-65a324f8 {
  margin-bottom: 20px;
}
.ml-20.data-v-65a324f8 {
  margin-left: 20px;
}
.mx-20.data-v-65a324f8 {
  margin-left: 20px;
  margin-right: 20px;
}
.my-20.data-v-65a324f8 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.p-20.data-v-65a324f8 {
  padding: 20px;
}
.pt-20.data-v-65a324f8 {
  padding-top: 20px;
}
.pr-20.data-v-65a324f8 {
  padding-right: 20px;
}
.pb-20.data-v-65a324f8 {
  padding-bottom: 20px;
}
.pl-20.data-v-65a324f8 {
  padding-left: 20px;
}
.px-20.data-v-65a324f8 {
  padding-left: 20px;
  padding-right: 20px;
}
.py-20.data-v-65a324f8 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.m-24.data-v-65a324f8 {
  margin: 24px;
}
.mt-24.data-v-65a324f8 {
  margin-top: 24px;
}
.mr-24.data-v-65a324f8 {
  margin-right: 24px;
}
.mb-24.data-v-65a324f8 {
  margin-bottom: 24px;
}
.ml-24.data-v-65a324f8 {
  margin-left: 24px;
}
.mx-24.data-v-65a324f8 {
  margin-left: 24px;
  margin-right: 24px;
}
.my-24.data-v-65a324f8 {
  margin-top: 24px;
  margin-bottom: 24px;
}
.p-24.data-v-65a324f8 {
  padding: 24px;
}
.pt-24.data-v-65a324f8 {
  padding-top: 24px;
}
.pr-24.data-v-65a324f8 {
  padding-right: 24px;
}
.pb-24.data-v-65a324f8 {
  padding-bottom: 24px;
}
.pl-24.data-v-65a324f8 {
  padding-left: 24px;
}
.px-24.data-v-65a324f8 {
  padding-left: 24px;
  padding-right: 24px;
}
.py-24.data-v-65a324f8 {
  padding-top: 24px;
  padding-bottom: 24px;
}
.m-32.data-v-65a324f8 {
  margin: 32px;
}
.mt-32.data-v-65a324f8 {
  margin-top: 32px;
}
.mr-32.data-v-65a324f8 {
  margin-right: 32px;
}
.mb-32.data-v-65a324f8 {
  margin-bottom: 32px;
}
.ml-32.data-v-65a324f8 {
  margin-left: 32px;
}
.mx-32.data-v-65a324f8 {
  margin-left: 32px;
  margin-right: 32px;
}
.my-32.data-v-65a324f8 {
  margin-top: 32px;
  margin-bottom: 32px;
}
.p-32.data-v-65a324f8 {
  padding: 32px;
}
.pt-32.data-v-65a324f8 {
  padding-top: 32px;
}
.pr-32.data-v-65a324f8 {
  padding-right: 32px;
}
.pb-32.data-v-65a324f8 {
  padding-bottom: 32px;
}
.pl-32.data-v-65a324f8 {
  padding-left: 32px;
}
.px-32.data-v-65a324f8 {
  padding-left: 32px;
  padding-right: 32px;
}
.py-32.data-v-65a324f8 {
  padding-top: 32px;
  padding-bottom: 32px;
}
.m-40.data-v-65a324f8 {
  margin: 40px;
}
.mt-40.data-v-65a324f8 {
  margin-top: 40px;
}
.mr-40.data-v-65a324f8 {
  margin-right: 40px;
}
.mb-40.data-v-65a324f8 {
  margin-bottom: 40px;
}
.ml-40.data-v-65a324f8 {
  margin-left: 40px;
}
.mx-40.data-v-65a324f8 {
  margin-left: 40px;
  margin-right: 40px;
}
.my-40.data-v-65a324f8 {
  margin-top: 40px;
  margin-bottom: 40px;
}
.p-40.data-v-65a324f8 {
  padding: 40px;
}
.pt-40.data-v-65a324f8 {
  padding-top: 40px;
}
.pr-40.data-v-65a324f8 {
  padding-right: 40px;
}
.pb-40.data-v-65a324f8 {
  padding-bottom: 40px;
}
.pl-40.data-v-65a324f8 {
  padding-left: 40px;
}
.px-40.data-v-65a324f8 {
  padding-left: 40px;
  padding-right: 40px;
}
.py-40.data-v-65a324f8 {
  padding-top: 40px;
  padding-bottom: 40px;
}
.m-48.data-v-65a324f8 {
  margin: 48px;
}
.mt-48.data-v-65a324f8 {
  margin-top: 48px;
}
.mr-48.data-v-65a324f8 {
  margin-right: 48px;
}
.mb-48.data-v-65a324f8 {
  margin-bottom: 48px;
}
.ml-48.data-v-65a324f8 {
  margin-left: 48px;
}
.mx-48.data-v-65a324f8 {
  margin-left: 48px;
  margin-right: 48px;
}
.my-48.data-v-65a324f8 {
  margin-top: 48px;
  margin-bottom: 48px;
}
.p-48.data-v-65a324f8 {
  padding: 48px;
}
.pt-48.data-v-65a324f8 {
  padding-top: 48px;
}
.pr-48.data-v-65a324f8 {
  padding-right: 48px;
}
.pb-48.data-v-65a324f8 {
  padding-bottom: 48px;
}
.pl-48.data-v-65a324f8 {
  padding-left: 48px;
}
.px-48.data-v-65a324f8 {
  padding-left: 48px;
  padding-right: 48px;
}
.py-48.data-v-65a324f8 {
  padding-top: 48px;
  padding-bottom: 48px;
}
@media (min-width: 1024px) {
.hover-lift.data-v-65a324f8 {
    transition: -webkit-transform 0.2s ease;
    transition: transform 0.2s ease;
    transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.hover-lift.data-v-65a324f8:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
}
.hover-scale.data-v-65a324f8 {
    transition: -webkit-transform 0.2s ease;
    transition: transform 0.2s ease;
    transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.hover-scale.data-v-65a324f8:hover {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
button.data-v-65a324f8, .button.data-v-65a324f8 {
    cursor: pointer;
    transition: all 0.2s ease;
}
button.data-v-65a324f8:hover, .button.data-v-65a324f8:hover {
    opacity: 0.8;
}
button.data-v-65a324f8:active, .button.data-v-65a324f8:active {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
}
._a.data-v-65a324f8, .link.data-v-65a324f8 {
    cursor: pointer;
    transition: color 0.2s ease;
}
input.data-v-65a324f8, textarea.data-v-65a324f8, .input.data-v-65a324f8 {
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
input.data-v-65a324f8:focus, textarea.data-v-65a324f8:focus, .input.data-v-65a324f8:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
}
@media (max-width: 1023px) {
.touch-feedback.data-v-65a324f8 {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}
.touch-feedback.data-v-65a324f8:active {
    background-color: rgba(0, 0, 0, 0.05);
}
button.data-v-65a324f8, .button.data-v-65a324f8 {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}
}
.gpu-accelerated.data-v-65a324f8 {
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.smooth-scroll.data-v-65a324f8 {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
@media (min-width: 1024px) {
page.data-v-65a324f8, page.data-v-65a324f8 {
    margin: 0;
    padding: 0;
    overflow: hidden;
}
.uni-page-head.data-v-65a324f8 {
    display: none !important;
}
.uni-page-wrapper.data-v-65a324f8 {
    height: 100vh !important;
}
.data-v-65a324f8::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.data-v-65a324f8::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}
.data-v-65a324f8::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}
.data-v-65a324f8::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
}
page.data-v-65a324f8 {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}
.button.data-v-65a324f8 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}
.button.primary.data-v-65a324f8 {
  background: #1890ff;
  color: white;
}
.button.primary.data-v-65a324f8:hover {
  background: #40a9ff;
}
.button.primary.data-v-65a324f8:active {
  background: #096dd9;
}
.button.secondary.data-v-65a324f8 {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}
.button.secondary.data-v-65a324f8:hover {
  background: #fafafa;
  border-color: #40a9ff;
}
@media (min-width: 1024px) {
page.data-v-65a324f8 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}
.uni-page-wrapper.data-v-65a324f8 {
    width: 375px !important;
    height: 667px !important;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
    -webkit-transform-origin: center;
            transform-origin: center;
    position: relative;
    transition: -webkit-transform 0.3s ease;
    transition: transform 0.3s ease;
    transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.uni-tabbar.data-v-65a324f8 {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    border-radius: 0 0 12px 12px;
}
.uni-page-head.data-v-65a324f8 {
    display: none !important;
}
}
.fade-enter-active.data-v-65a324f8,
.fade-leave-active.data-v-65a324f8 {
  transition: opacity 0.3s ease;
}
.fade-enter.data-v-65a324f8,
.fade-leave-to.data-v-65a324f8 {
  opacity: 0;
}
.slide-enter-active.data-v-65a324f8,
.slide-leave-active.data-v-65a324f8 {
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.slide-enter.data-v-65a324f8,
.slide-leave-to.data-v-65a324f8 {
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
}
/**
 * 多端适配样式系统
 * 支持PC端缩放、响应式布局和平台特定优化
 */
.multi-platform-pc.data-v-65a324f8 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  margin: 0;
  padding: 20px;
  transition: all 0.3s ease;
}
.multi-platform-pc .uni-app.data-v-65a324f8,
.multi-platform-pc #app.data-v-65a324f8 {
  width: 375px !important;
  height: 667px !important;
  max-width: 375px !important;
  max-height: 667px !important;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  -webkit-transform-origin: center;
          transform-origin: center;
  position: relative;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.multi-platform-pc .uni-page-wrapper.data-v-65a324f8 {
  border-radius: 12px;
  overflow: hidden;
  height: 100% !important;
}
.multi-platform-pc .uni-tabbar.data-v-65a324f8 {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  border-radius: 0 0 12px 12px;
}
@media (max-width: 1023px) {
.multi-platform-pc.data-v-65a324f8 {
    background: #f5f5f5;
    padding: 0;
    display: block;
}
.multi-platform-pc .uni-app.data-v-65a324f8,
  .multi-platform-pc #app.data-v-65a324f8 {
    width: 100% !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0;
    box-shadow: none;
    -webkit-transform: none;
            transform: none;
}
.multi-platform-pc .uni-page-wrapper.data-v-65a324f8 {
    border-radius: 0;
}
.multi-platform-pc .uni-tabbar.data-v-65a324f8 {
    position: fixed !important;
}
}
.responsive-container.data-v-65a324f8, .container.data-v-65a324f8 {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-container.data-v-65a324f8, .container.data-v-65a324f8 {
    max-width: 750px;
    padding: 0 24px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-container.data-v-65a324f8, .container.data-v-65a324f8 {
    max-width: 1000px;
    padding: 0 32px;
}
}
@media (min-width: 1440px) {
.responsive-container.data-v-65a324f8, .container.data-v-65a324f8 {
    max-width: 1200px;
    padding: 0 40px;
}
}
.responsive-grid.data-v-65a324f8 {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-grid.data-v-65a324f8 {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-grid.data-v-65a324f8 {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
}
}
@media (min-width: 1440px) {
.responsive-grid.data-v-65a324f8 {
    grid-template-columns: repeat(4, 1fr);
    gap: 28px;
}
}
.responsive-card.data-v-65a324f8, .card.data-v-65a324f8 {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-card.data-v-65a324f8, .card.data-v-65a324f8 {
    padding: 20px;
    border-radius: 10px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-card.data-v-65a324f8, .card.data-v-65a324f8 {
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
.responsive-card.data-v-65a324f8:hover, .card.data-v-65a324f8:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}
}
.platform-h5.platform-h5-pc .uni-page-head.data-v-65a324f8 {
  display: none !important;
}
.show-mobile.data-v-65a324f8 {
  display: block;
}
@media (min-width: 768px) and (max-width: 1023px) {
.show-mobile.data-v-65a324f8 {
    display: none;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.show-mobile.data-v-65a324f8 {
    display: none;
}
}
@media (min-width: 1440px) {
.show-mobile.data-v-65a324f8 {
    display: none;
}
}
.show-tablet.data-v-65a324f8 {
  display: none;
}
@media (min-width: 768px) and (max-width: 1023px) {
.show-tablet.data-v-65a324f8 {
    display: block;
}
}
.show-desktop.data-v-65a324f8 {
  display: none;
}
@media (min-width: 1024px) and (max-width: 1439px) {
.show-desktop.data-v-65a324f8 {
    display: block;
}
}
.show-large.data-v-65a324f8 {
  display: none;
}
@media (min-width: 1440px) {
.show-large.data-v-65a324f8 {
    display: block;
}
}
@media (min-width: 0px) and (max-width: 767px) {
.hide-mobile.data-v-65a324f8 {
    display: none !important;
}
}
@media (min-width: 768px) and (max-width: 1023px) {
.hide-tablet.data-v-65a324f8 {
    display: none !important;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.hide-desktop.data-v-65a324f8 {
    display: none !important;
}
}
@media (min-width: 1440px) {
.hide-large.data-v-65a324f8 {
    display: none !important;
}
}
@media (max-width: 1023px) {
.pc-only.data-v-65a324f8 {
    display: none !important;
}
}
@media (min-width: 1024px) {
.mobile-only.data-v-65a324f8 {
    display: none !important;
}
}
.platform-show-h5.data-v-65a324f8 {
  display: none;
}
.platform-h5 .platform-show-h5.data-v-65a324f8 {
  display: block;
}
.platform-show-app.data-v-65a324f8 {
  display: none;
}
.platform-app .platform-show-app.data-v-65a324f8 {
  display: block;
}
.platform-show-mp.data-v-65a324f8 {
  display: none;
}
.platform-mp-weixin .platform-show-mp.data-v-65a324f8 {
  display: block;
}
.platform-mp-alipay .platform-show-mp.data-v-65a324f8 {
  display: block;
}
.platform-mp-baidu .platform-show-mp.data-v-65a324f8 {
  display: block;
}
.flex.data-v-65a324f8 {
  display: flex;
}
.flex-column.data-v-65a324f8 {
  flex-direction: column;
}
.flex-row.data-v-65a324f8 {
  flex-direction: row;
}
.flex-wrap.data-v-65a324f8 {
  flex-wrap: wrap;
}
.justify-center.data-v-65a324f8 {
  justify-content: center;
}
.justify-between.data-v-65a324f8 {
  justify-content: space-between;
}
.justify-around.data-v-65a324f8 {
  justify-content: space-around;
}
.align-center.data-v-65a324f8 {
  align-items: center;
}
.align-start.data-v-65a324f8 {
  align-items: flex-start;
}
.align-end.data-v-65a324f8 {
  align-items: flex-end;
}
.text-left.data-v-65a324f8 {
  text-align: left;
}
.text-center.data-v-65a324f8 {
  text-align: center;
}
.text-right.data-v-65a324f8 {
  text-align: right;
}
.m-0.data-v-65a324f8 {
  margin: 0px;
}
.mt-0.data-v-65a324f8 {
  margin-top: 0px;
}
.mr-0.data-v-65a324f8 {
  margin-right: 0px;
}
.mb-0.data-v-65a324f8 {
  margin-bottom: 0px;
}
.ml-0.data-v-65a324f8 {
  margin-left: 0px;
}
.mx-0.data-v-65a324f8 {
  margin-left: 0px;
  margin-right: 0px;
}
.my-0.data-v-65a324f8 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.p-0.data-v-65a324f8 {
  padding: 0px;
}
.pt-0.data-v-65a324f8 {
  padding-top: 0px;
}
.pr-0.data-v-65a324f8 {
  padding-right: 0px;
}
.pb-0.data-v-65a324f8 {
  padding-bottom: 0px;
}
.pl-0.data-v-65a324f8 {
  padding-left: 0px;
}
.px-0.data-v-65a324f8 {
  padding-left: 0px;
  padding-right: 0px;
}
.py-0.data-v-65a324f8 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.m-4.data-v-65a324f8 {
  margin: 4px;
}
.mt-4.data-v-65a324f8 {
  margin-top: 4px;
}
.mr-4.data-v-65a324f8 {
  margin-right: 4px;
}
.mb-4.data-v-65a324f8 {
  margin-bottom: 4px;
}
.ml-4.data-v-65a324f8 {
  margin-left: 4px;
}
.mx-4.data-v-65a324f8 {
  margin-left: 4px;
  margin-right: 4px;
}
.my-4.data-v-65a324f8 {
  margin-top: 4px;
  margin-bottom: 4px;
}
.p-4.data-v-65a324f8 {
  padding: 4px;
}
.pt-4.data-v-65a324f8 {
  padding-top: 4px;
}
.pr-4.data-v-65a324f8 {
  padding-right: 4px;
}
.pb-4.data-v-65a324f8 {
  padding-bottom: 4px;
}
.pl-4.data-v-65a324f8 {
  padding-left: 4px;
}
.px-4.data-v-65a324f8 {
  padding-left: 4px;
  padding-right: 4px;
}
.py-4.data-v-65a324f8 {
  padding-top: 4px;
  padding-bottom: 4px;
}
.m-8.data-v-65a324f8 {
  margin: 8px;
}
.mt-8.data-v-65a324f8 {
  margin-top: 8px;
}
.mr-8.data-v-65a324f8 {
  margin-right: 8px;
}
.mb-8.data-v-65a324f8 {
  margin-bottom: 8px;
}
.ml-8.data-v-65a324f8 {
  margin-left: 8px;
}
.mx-8.data-v-65a324f8 {
  margin-left: 8px;
  margin-right: 8px;
}
.my-8.data-v-65a324f8 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.p-8.data-v-65a324f8 {
  padding: 8px;
}
.pt-8.data-v-65a324f8 {
  padding-top: 8px;
}
.pr-8.data-v-65a324f8 {
  padding-right: 8px;
}
.pb-8.data-v-65a324f8 {
  padding-bottom: 8px;
}
.pl-8.data-v-65a324f8 {
  padding-left: 8px;
}
.px-8.data-v-65a324f8 {
  padding-left: 8px;
  padding-right: 8px;
}
.py-8.data-v-65a324f8 {
  padding-top: 8px;
  padding-bottom: 8px;
}
.m-12.data-v-65a324f8 {
  margin: 12px;
}
.mt-12.data-v-65a324f8 {
  margin-top: 12px;
}
.mr-12.data-v-65a324f8 {
  margin-right: 12px;
}
.mb-12.data-v-65a324f8 {
  margin-bottom: 12px;
}
.ml-12.data-v-65a324f8 {
  margin-left: 12px;
}
.mx-12.data-v-65a324f8 {
  margin-left: 12px;
  margin-right: 12px;
}
.my-12.data-v-65a324f8 {
  margin-top: 12px;
  margin-bottom: 12px;
}
.p-12.data-v-65a324f8 {
  padding: 12px;
}
.pt-12.data-v-65a324f8 {
  padding-top: 12px;
}
.pr-12.data-v-65a324f8 {
  padding-right: 12px;
}
.pb-12.data-v-65a324f8 {
  padding-bottom: 12px;
}
.pl-12.data-v-65a324f8 {
  padding-left: 12px;
}
.px-12.data-v-65a324f8 {
  padding-left: 12px;
  padding-right: 12px;
}
.py-12.data-v-65a324f8 {
  padding-top: 12px;
  padding-bottom: 12px;
}
.m-16.data-v-65a324f8 {
  margin: 16px;
}
.mt-16.data-v-65a324f8 {
  margin-top: 16px;
}
.mr-16.data-v-65a324f8 {
  margin-right: 16px;
}
.mb-16.data-v-65a324f8 {
  margin-bottom: 16px;
}
.ml-16.data-v-65a324f8 {
  margin-left: 16px;
}
.mx-16.data-v-65a324f8 {
  margin-left: 16px;
  margin-right: 16px;
}
.my-16.data-v-65a324f8 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.p-16.data-v-65a324f8 {
  padding: 16px;
}
.pt-16.data-v-65a324f8 {
  padding-top: 16px;
}
.pr-16.data-v-65a324f8 {
  padding-right: 16px;
}
.pb-16.data-v-65a324f8 {
  padding-bottom: 16px;
}
.pl-16.data-v-65a324f8 {
  padding-left: 16px;
}
.px-16.data-v-65a324f8 {
  padding-left: 16px;
  padding-right: 16px;
}
.py-16.data-v-65a324f8 {
  padding-top: 16px;
  padding-bottom: 16px;
}
.m-20.data-v-65a324f8 {
  margin: 20px;
}
.mt-20.data-v-65a324f8 {
  margin-top: 20px;
}
.mr-20.data-v-65a324f8 {
  margin-right: 20px;
}
.mb-20.data-v-65a324f8 {
  margin-bottom: 20px;
}
.ml-20.data-v-65a324f8 {
  margin-left: 20px;
}
.mx-20.data-v-65a324f8 {
  margin-left: 20px;
  margin-right: 20px;
}
.my-20.data-v-65a324f8 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.p-20.data-v-65a324f8 {
  padding: 20px;
}
.pt-20.data-v-65a324f8 {
  padding-top: 20px;
}
.pr-20.data-v-65a324f8 {
  padding-right: 20px;
}
.pb-20.data-v-65a324f8 {
  padding-bottom: 20px;
}
.pl-20.data-v-65a324f8 {
  padding-left: 20px;
}
.px-20.data-v-65a324f8 {
  padding-left: 20px;
  padding-right: 20px;
}
.py-20.data-v-65a324f8 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.m-24.data-v-65a324f8 {
  margin: 24px;
}
.mt-24.data-v-65a324f8 {
  margin-top: 24px;
}
.mr-24.data-v-65a324f8 {
  margin-right: 24px;
}
.mb-24.data-v-65a324f8 {
  margin-bottom: 24px;
}
.ml-24.data-v-65a324f8 {
  margin-left: 24px;
}
.mx-24.data-v-65a324f8 {
  margin-left: 24px;
  margin-right: 24px;
}
.my-24.data-v-65a324f8 {
  margin-top: 24px;
  margin-bottom: 24px;
}
.p-24.data-v-65a324f8 {
  padding: 24px;
}
.pt-24.data-v-65a324f8 {
  padding-top: 24px;
}
.pr-24.data-v-65a324f8 {
  padding-right: 24px;
}
.pb-24.data-v-65a324f8 {
  padding-bottom: 24px;
}
.pl-24.data-v-65a324f8 {
  padding-left: 24px;
}
.px-24.data-v-65a324f8 {
  padding-left: 24px;
  padding-right: 24px;
}
.py-24.data-v-65a324f8 {
  padding-top: 24px;
  padding-bottom: 24px;
}
.m-32.data-v-65a324f8 {
  margin: 32px;
}
.mt-32.data-v-65a324f8 {
  margin-top: 32px;
}
.mr-32.data-v-65a324f8 {
  margin-right: 32px;
}
.mb-32.data-v-65a324f8 {
  margin-bottom: 32px;
}
.ml-32.data-v-65a324f8 {
  margin-left: 32px;
}
.mx-32.data-v-65a324f8 {
  margin-left: 32px;
  margin-right: 32px;
}
.my-32.data-v-65a324f8 {
  margin-top: 32px;
  margin-bottom: 32px;
}
.p-32.data-v-65a324f8 {
  padding: 32px;
}
.pt-32.data-v-65a324f8 {
  padding-top: 32px;
}
.pr-32.data-v-65a324f8 {
  padding-right: 32px;
}
.pb-32.data-v-65a324f8 {
  padding-bottom: 32px;
}
.pl-32.data-v-65a324f8 {
  padding-left: 32px;
}
.px-32.data-v-65a324f8 {
  padding-left: 32px;
  padding-right: 32px;
}
.py-32.data-v-65a324f8 {
  padding-top: 32px;
  padding-bottom: 32px;
}
.m-40.data-v-65a324f8 {
  margin: 40px;
}
.mt-40.data-v-65a324f8 {
  margin-top: 40px;
}
.mr-40.data-v-65a324f8 {
  margin-right: 40px;
}
.mb-40.data-v-65a324f8 {
  margin-bottom: 40px;
}
.ml-40.data-v-65a324f8 {
  margin-left: 40px;
}
.mx-40.data-v-65a324f8 {
  margin-left: 40px;
  margin-right: 40px;
}
.my-40.data-v-65a324f8 {
  margin-top: 40px;
  margin-bottom: 40px;
}
.p-40.data-v-65a324f8 {
  padding: 40px;
}
.pt-40.data-v-65a324f8 {
  padding-top: 40px;
}
.pr-40.data-v-65a324f8 {
  padding-right: 40px;
}
.pb-40.data-v-65a324f8 {
  padding-bottom: 40px;
}
.pl-40.data-v-65a324f8 {
  padding-left: 40px;
}
.px-40.data-v-65a324f8 {
  padding-left: 40px;
  padding-right: 40px;
}
.py-40.data-v-65a324f8 {
  padding-top: 40px;
  padding-bottom: 40px;
}
.m-48.data-v-65a324f8 {
  margin: 48px;
}
.mt-48.data-v-65a324f8 {
  margin-top: 48px;
}
.mr-48.data-v-65a324f8 {
  margin-right: 48px;
}
.mb-48.data-v-65a324f8 {
  margin-bottom: 48px;
}
.ml-48.data-v-65a324f8 {
  margin-left: 48px;
}
.mx-48.data-v-65a324f8 {
  margin-left: 48px;
  margin-right: 48px;
}
.my-48.data-v-65a324f8 {
  margin-top: 48px;
  margin-bottom: 48px;
}
.p-48.data-v-65a324f8 {
  padding: 48px;
}
.pt-48.data-v-65a324f8 {
  padding-top: 48px;
}
.pr-48.data-v-65a324f8 {
  padding-right: 48px;
}
.pb-48.data-v-65a324f8 {
  padding-bottom: 48px;
}
.pl-48.data-v-65a324f8 {
  padding-left: 48px;
}
.px-48.data-v-65a324f8 {
  padding-left: 48px;
  padding-right: 48px;
}
.py-48.data-v-65a324f8 {
  padding-top: 48px;
  padding-bottom: 48px;
}
@media (min-width: 1024px) {
.hover-lift.data-v-65a324f8 {
    transition: -webkit-transform 0.2s ease;
    transition: transform 0.2s ease;
    transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.hover-lift.data-v-65a324f8:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
}
.hover-scale.data-v-65a324f8 {
    transition: -webkit-transform 0.2s ease;
    transition: transform 0.2s ease;
    transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.hover-scale.data-v-65a324f8:hover {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
button.data-v-65a324f8, .button.data-v-65a324f8 {
    cursor: pointer;
    transition: all 0.2s ease;
}
button.data-v-65a324f8:hover, .button.data-v-65a324f8:hover {
    opacity: 0.8;
}
button.data-v-65a324f8:active, .button.data-v-65a324f8:active {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
}
._a.data-v-65a324f8, .link.data-v-65a324f8 {
    cursor: pointer;
    transition: color 0.2s ease;
}
input.data-v-65a324f8, textarea.data-v-65a324f8, .input.data-v-65a324f8 {
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
input.data-v-65a324f8:focus, textarea.data-v-65a324f8:focus, .input.data-v-65a324f8:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
}
@media (max-width: 1023px) {
.touch-feedback.data-v-65a324f8 {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}
.touch-feedback.data-v-65a324f8:active {
    background-color: rgba(0, 0, 0, 0.05);
}
button.data-v-65a324f8, .button.data-v-65a324f8 {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}
}
.gpu-accelerated.data-v-65a324f8 {
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.smooth-scroll.data-v-65a324f8 {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
@media (min-width: 1024px) {
page.data-v-65a324f8, page.data-v-65a324f8 {
    margin: 0;
    padding: 0;
    overflow: hidden;
}
.uni-page-head.data-v-65a324f8 {
    display: none !important;
}
.uni-page-wrapper.data-v-65a324f8 {
    height: 100vh !important;
}
.data-v-65a324f8::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.data-v-65a324f8::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}
.data-v-65a324f8::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}
.data-v-65a324f8::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
}
.responsive-container.data-v-65a324f8, .container.data-v-65a324f8 {
  width: 100%;
  box-sizing: border-box;
  transition: all 0.3s ease;
}
.responsive-container--center.data-v-65a324f8 {
  margin-left: auto;
  margin-right: auto;
}
.responsive-container--fluid.data-v-65a324f8 {
  max-width: none !important;
}
@media (min-width: 1024px) {
.responsive-container--pc.data-v-65a324f8 {
    min-height: auto;
}
}
@media (min-width: 1024px) {
.responsive-container--pc-optimized.data-v-65a324f8 {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.responsive-container--pc-optimized.data-v-65a324f8:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}
}
.responsive-container--shadow.data-v-65a324f8 {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.responsive-container--shadow-sm.data-v-65a324f8 {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
.responsive-container--shadow-md.data-v-65a324f8 {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.responsive-container--shadow-lg.data-v-65a324f8 {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}
.responsive-container--shadow-xl.data-v-65a324f8 {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}
@media (min-width: 0px) and (max-width: 767px) {
.responsive-container--mobile.data-v-65a324f8 {
    padding: 16px;
}
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-container--tablet.data-v-65a324f8 {
    padding: 24px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-container--desktop.data-v-65a324f8 {
    padding: 32px;
}
}
@media (min-width: 1440px) {
.responsive-container--large.data-v-65a324f8 {
    padding: 40px;
}
}

