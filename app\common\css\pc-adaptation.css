/**
 * PC端适配样式
 * 针对微信小程序PC端的特殊适配
 */

/* ==================== PC端基础适配 ==================== */

/* 微信小程序PC端适配 */
.mp-weixin-pc {
  /* 确保PC端正常显示 */
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* PC端页面容器 */
.mp-weixin-pc .uni-page-wrapper {
  max-width: 100%;
  margin: 0 auto;
  background-color: #fff;
}

/* ==================== 响应式断点适配 ==================== */

/* 移动端样式 (0-767px) */
.screen-mobile {
  /* 移动端默认样式 */
}

/* 平板端样式 (768-1023px) */
.screen-tablet {
  /* 平板端适配 */
  .uni-page-wrapper {
    max-width: 768px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

/* 桌面端样式 (1024-1439px) */
.screen-desktop {
  /* 桌面端适配 */
  .uni-page-wrapper {
    max-width: 1024px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

/* 大屏样式 (1440px+) */
.screen-large {
  /* 大屏适配 */
  .uni-page-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

/* ==================== PC端组件优化 ==================== */

/* PC端按钮优化 */
.mp-weixin-pc .cu-btn {
  cursor: pointer;
  transition: all 0.2s ease;
}

.mp-weixin-pc .cu-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* PC端卡片优化 */
.mp-weixin-pc .cu-card {
  transition: all 0.2s ease;
}

.mp-weixin-pc .cu-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* PC端列表项优化 */
.mp-weixin-pc .cu-item {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mp-weixin-pc .cu-item:hover {
  background-color: #f8f9fa;
}

/* ==================== 导航栏适配 ==================== */

/* PC端导航栏 - 微信小程序PC端使用系统导航栏 */
.mp-weixin-pc .cu-custom {
  /* PC端隐藏自定义导航栏，使用系统导航栏 */
  display: none;
}

/* ==================== 布局优化 ==================== */

/* PC端网格布局优化 */
.mp-weixin-pc .modern-grid {
  display: grid;
  gap: 16px;
}

/* 移动端：2列 */
.screen-mobile .modern-grid {
  grid-template-columns: repeat(2, 1fr);
}

/* 平板端：3列 */
.screen-tablet .modern-grid {
  grid-template-columns: repeat(3, 1fr);
}

/* 桌面端：4列 */
.screen-desktop .modern-grid {
  grid-template-columns: repeat(4, 1fr);
}

/* 大屏：5列 */
.screen-large .modern-grid {
  grid-template-columns: repeat(5, 1fr);
}

/* ==================== 窗口模式适配 ==================== */

/* 小窗口模式 (ratio < 0.75) */
.window-mode-small {
  .modern-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .cu-card {
    margin: 8px;
  }
}

/* 正常窗口模式 (0.75 <= ratio < 1.5) */
.window-mode-normal {
  /* 使用默认样式 */
}

/* 大窗口模式 (1.5 <= ratio < 2.0) */
.window-mode-large {
  .modern-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
  
  .cu-card {
    margin: 16px;
  }
}

/* 双栏模式 (ratio >= 2.0) */
.window-mode-dual-column {
  .modern-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 24px;
  }
  
  .cu-card {
    margin: 20px;
  }
}

/* ==================== 交互优化 ==================== */

/* PC端滚动条美化 */
.mp-weixin-pc ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.mp-weixin-pc ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.mp-weixin-pc ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.mp-weixin-pc ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ==================== 动画优化 ==================== */

/* PC端减少动画时长，提升响应速度 */
.mp-weixin-pc * {
  transition-duration: 0.2s !important;
}

/* ==================== 文字优化 ==================== */

/* PC端文字大小优化 */
.mp-weixin-pc {
  font-size: 14px;
  line-height: 1.5;
}

.mp-weixin-pc .text-sm {
  font-size: 12px;
}

.mp-weixin-pc .text-lg {
  font-size: 16px;
}

.mp-weixin-pc .text-xl {
  font-size: 18px;
}

/* ==================== 调试样式 ==================== */

/* PC端调试信息 */
.pc-debug-info {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  font-family: monospace;
}

/* ==================== 媒体查询补充 ==================== */

/* 确保在不同屏幕尺寸下的适配 */
@media (max-width: 767px) {
  .mp-weixin-pc .modern-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .mp-weixin-pc .modern-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (min-width: 1024px) and (max-width: 1439px) {
  .mp-weixin-pc .modern-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

@media (min-width: 1440px) {
  .mp-weixin-pc .modern-grid {
    grid-template-columns: repeat(5, 1fr) !important;
  }
}
