<view class="pc-demo data-v-5ca1ce02"><view class="header data-v-5ca1ce02"><text class="title data-v-5ca1ce02">PC端适配演示</text><text class="subtitle data-v-5ca1ce02">在PC端自动缩放，保持移动端体验</text></view><view class="status-card data-v-5ca1ce02"><view class="status-item data-v-5ca1ce02"><text class="status-label data-v-5ca1ce02">设备类型：</text><text class="{{['status-value','data-v-5ca1ce02',(isPC)?'pc-device':'']}}">{{''+(isPC?'PC端':'移动端')+''}}</text></view><block wx:if="{{isPC}}"><view class="status-item data-v-5ca1ce02"><text class="status-label data-v-5ca1ce02">缩放比例：</text><text class="status-value data-v-5ca1ce02">{{scaleRatio}}</text></view></block><view class="status-item data-v-5ca1ce02"><text class="status-label data-v-5ca1ce02">窗口大小：</text><text class="status-value data-v-5ca1ce02">{{windowSize.width+"x"+windowSize.height}}</text></view></view><view class="demo-section data-v-5ca1ce02"><view class="section-title data-v-5ca1ce02">功能演示</view><view class="card-list data-v-5ca1ce02"><block wx:for="{{demoItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="demo-card data-v-5ca1ce02"><view class="card-icon data-v-5ca1ce02">{{item.icon}}</view><view class="card-content data-v-5ca1ce02"><text class="card-title data-v-5ca1ce02">{{item.title}}</text><text class="card-desc data-v-5ca1ce02">{{item.desc}}</text></view></view></block></view></view><view class="interaction-section data-v-5ca1ce02"><view class="section-title data-v-5ca1ce02">交互演示</view><view class="button-grid data-v-5ca1ce02"><button data-event-opts="{{[['tap',[['showMessage',['主要按钮']]]]]}}" class="demo-btn primary data-v-5ca1ce02" bindtap="__e">主要按钮</button><button data-event-opts="{{[['tap',[['showMessage',['次要按钮']]]]]}}" class="demo-btn secondary data-v-5ca1ce02" bindtap="__e">次要按钮</button><button data-event-opts="{{[['tap',[['showMessage',['成功按钮']]]]]}}" class="demo-btn success data-v-5ca1ce02" bindtap="__e">成功按钮</button><button data-event-opts="{{[['tap',[['showMessage',['警告按钮']]]]]}}" class="demo-btn warning data-v-5ca1ce02" bindtap="__e">警告按钮</button></view></view><view class="footer data-v-5ca1ce02"><text class="footer-text data-v-5ca1ce02">{{''+(isPC?'PC端已自动缩放适配':'移动端原生显示')+''}}</text></view></view>