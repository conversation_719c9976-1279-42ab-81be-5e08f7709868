@charset "UTF-8";
/**
 * uni-app全局样式 - 多端适配版本
 */
/**
 * 多端适配样式系统
 * 支持PC端缩放、响应式布局和平台特定优化
 */
.multi-platform-pc.data-v-ab6b2dd0 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  margin: 0;
  padding: 20px;
  transition: all 0.3s ease;
}
.multi-platform-pc .uni-app.data-v-ab6b2dd0,
.multi-platform-pc #app.data-v-ab6b2dd0 {
  width: 375px !important;
  height: 667px !important;
  max-width: 375px !important;
  max-height: 667px !important;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  -webkit-transform-origin: center;
          transform-origin: center;
  position: relative;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.multi-platform-pc .uni-page-wrapper.data-v-ab6b2dd0 {
  border-radius: 12px;
  overflow: hidden;
  height: 100% !important;
}
.multi-platform-pc .uni-tabbar.data-v-ab6b2dd0 {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  border-radius: 0 0 12px 12px;
}
@media (max-width: 1023px) {
.multi-platform-pc.data-v-ab6b2dd0 {
    background: #f5f5f5;
    padding: 0;
    display: block;
}
.multi-platform-pc .uni-app.data-v-ab6b2dd0,
  .multi-platform-pc #app.data-v-ab6b2dd0 {
    width: 100% !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0;
    box-shadow: none;
    -webkit-transform: none;
            transform: none;
}
.multi-platform-pc .uni-page-wrapper.data-v-ab6b2dd0 {
    border-radius: 0;
}
.multi-platform-pc .uni-tabbar.data-v-ab6b2dd0 {
    position: fixed !important;
}
}
.responsive-container.data-v-ab6b2dd0, .container.data-v-ab6b2dd0 {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-container.data-v-ab6b2dd0, .container.data-v-ab6b2dd0 {
    max-width: 750px;
    padding: 0 24px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-container.data-v-ab6b2dd0, .container.data-v-ab6b2dd0 {
    max-width: 1000px;
    padding: 0 32px;
}
}
@media (min-width: 1440px) {
.responsive-container.data-v-ab6b2dd0, .container.data-v-ab6b2dd0 {
    max-width: 1200px;
    padding: 0 40px;
}
}
.responsive-grid.data-v-ab6b2dd0 {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-grid.data-v-ab6b2dd0 {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-grid.data-v-ab6b2dd0 {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
}
}
@media (min-width: 1440px) {
.responsive-grid.data-v-ab6b2dd0 {
    grid-template-columns: repeat(4, 1fr);
    gap: 28px;
}
}
.responsive-card.data-v-ab6b2dd0, .card.data-v-ab6b2dd0 {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-card.data-v-ab6b2dd0, .card.data-v-ab6b2dd0 {
    padding: 20px;
    border-radius: 10px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-card.data-v-ab6b2dd0, .card.data-v-ab6b2dd0 {
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
.responsive-card.data-v-ab6b2dd0:hover, .card.data-v-ab6b2dd0:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}
}
.platform-h5.platform-h5-pc .uni-page-head.data-v-ab6b2dd0 {
  display: none !important;
}
.show-mobile.data-v-ab6b2dd0 {
  display: block;
}
@media (min-width: 768px) and (max-width: 1023px) {
.show-mobile.data-v-ab6b2dd0 {
    display: none;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.show-mobile.data-v-ab6b2dd0 {
    display: none;
}
}
@media (min-width: 1440px) {
.show-mobile.data-v-ab6b2dd0 {
    display: none;
}
}
.show-tablet.data-v-ab6b2dd0 {
  display: none;
}
@media (min-width: 768px) and (max-width: 1023px) {
.show-tablet.data-v-ab6b2dd0 {
    display: block;
}
}
.show-desktop.data-v-ab6b2dd0 {
  display: none;
}
@media (min-width: 1024px) and (max-width: 1439px) {
.show-desktop.data-v-ab6b2dd0 {
    display: block;
}
}
.show-large.data-v-ab6b2dd0 {
  display: none;
}
@media (min-width: 1440px) {
.show-large.data-v-ab6b2dd0 {
    display: block;
}
}
@media (min-width: 0px) and (max-width: 767px) {
.hide-mobile.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (min-width: 768px) and (max-width: 1023px) {
.hide-tablet.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.hide-desktop.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (min-width: 1440px) {
.hide-large.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (max-width: 1023px) {
.pc-only.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (min-width: 1024px) {
.mobile-only.data-v-ab6b2dd0 {
    display: none !important;
}
}
.platform-show-h5.data-v-ab6b2dd0 {
  display: none;
}
.platform-h5 .platform-show-h5.data-v-ab6b2dd0 {
  display: block;
}
.platform-show-app.data-v-ab6b2dd0 {
  display: none;
}
.platform-app .platform-show-app.data-v-ab6b2dd0 {
  display: block;
}
.platform-show-mp.data-v-ab6b2dd0 {
  display: none;
}
.platform-mp-weixin .platform-show-mp.data-v-ab6b2dd0 {
  display: block;
}
.platform-mp-alipay .platform-show-mp.data-v-ab6b2dd0 {
  display: block;
}
.platform-mp-baidu .platform-show-mp.data-v-ab6b2dd0 {
  display: block;
}
.flex.data-v-ab6b2dd0 {
  display: flex;
}
.flex-column.data-v-ab6b2dd0 {
  flex-direction: column;
}
.flex-row.data-v-ab6b2dd0 {
  flex-direction: row;
}
.flex-wrap.data-v-ab6b2dd0 {
  flex-wrap: wrap;
}
.justify-center.data-v-ab6b2dd0 {
  justify-content: center;
}
.justify-between.data-v-ab6b2dd0 {
  justify-content: space-between;
}
.justify-around.data-v-ab6b2dd0 {
  justify-content: space-around;
}
.align-center.data-v-ab6b2dd0 {
  align-items: center;
}
.align-start.data-v-ab6b2dd0 {
  align-items: flex-start;
}
.align-end.data-v-ab6b2dd0 {
  align-items: flex-end;
}
.text-left.data-v-ab6b2dd0 {
  text-align: left;
}
.text-center.data-v-ab6b2dd0 {
  text-align: center;
}
.text-right.data-v-ab6b2dd0 {
  text-align: right;
}
.m-0.data-v-ab6b2dd0 {
  margin: 0px;
}
.mt-0.data-v-ab6b2dd0 {
  margin-top: 0px;
}
.mr-0.data-v-ab6b2dd0 {
  margin-right: 0px;
}
.mb-0.data-v-ab6b2dd0 {
  margin-bottom: 0px;
}
.ml-0.data-v-ab6b2dd0 {
  margin-left: 0px;
}
.mx-0.data-v-ab6b2dd0 {
  margin-left: 0px;
  margin-right: 0px;
}
.my-0.data-v-ab6b2dd0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.p-0.data-v-ab6b2dd0 {
  padding: 0px;
}
.pt-0.data-v-ab6b2dd0 {
  padding-top: 0px;
}
.pr-0.data-v-ab6b2dd0 {
  padding-right: 0px;
}
.pb-0.data-v-ab6b2dd0 {
  padding-bottom: 0px;
}
.pl-0.data-v-ab6b2dd0 {
  padding-left: 0px;
}
.px-0.data-v-ab6b2dd0 {
  padding-left: 0px;
  padding-right: 0px;
}
.py-0.data-v-ab6b2dd0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.m-4.data-v-ab6b2dd0 {
  margin: 4px;
}
.mt-4.data-v-ab6b2dd0 {
  margin-top: 4px;
}
.mr-4.data-v-ab6b2dd0 {
  margin-right: 4px;
}
.mb-4.data-v-ab6b2dd0 {
  margin-bottom: 4px;
}
.ml-4.data-v-ab6b2dd0 {
  margin-left: 4px;
}
.mx-4.data-v-ab6b2dd0 {
  margin-left: 4px;
  margin-right: 4px;
}
.my-4.data-v-ab6b2dd0 {
  margin-top: 4px;
  margin-bottom: 4px;
}
.p-4.data-v-ab6b2dd0 {
  padding: 4px;
}
.pt-4.data-v-ab6b2dd0 {
  padding-top: 4px;
}
.pr-4.data-v-ab6b2dd0 {
  padding-right: 4px;
}
.pb-4.data-v-ab6b2dd0 {
  padding-bottom: 4px;
}
.pl-4.data-v-ab6b2dd0 {
  padding-left: 4px;
}
.px-4.data-v-ab6b2dd0 {
  padding-left: 4px;
  padding-right: 4px;
}
.py-4.data-v-ab6b2dd0 {
  padding-top: 4px;
  padding-bottom: 4px;
}
.m-8.data-v-ab6b2dd0 {
  margin: 8px;
}
.mt-8.data-v-ab6b2dd0 {
  margin-top: 8px;
}
.mr-8.data-v-ab6b2dd0 {
  margin-right: 8px;
}
.mb-8.data-v-ab6b2dd0 {
  margin-bottom: 8px;
}
.ml-8.data-v-ab6b2dd0 {
  margin-left: 8px;
}
.mx-8.data-v-ab6b2dd0 {
  margin-left: 8px;
  margin-right: 8px;
}
.my-8.data-v-ab6b2dd0 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.p-8.data-v-ab6b2dd0 {
  padding: 8px;
}
.pt-8.data-v-ab6b2dd0 {
  padding-top: 8px;
}
.pr-8.data-v-ab6b2dd0 {
  padding-right: 8px;
}
.pb-8.data-v-ab6b2dd0 {
  padding-bottom: 8px;
}
.pl-8.data-v-ab6b2dd0 {
  padding-left: 8px;
}
.px-8.data-v-ab6b2dd0 {
  padding-left: 8px;
  padding-right: 8px;
}
.py-8.data-v-ab6b2dd0 {
  padding-top: 8px;
  padding-bottom: 8px;
}
.m-12.data-v-ab6b2dd0 {
  margin: 12px;
}
.mt-12.data-v-ab6b2dd0 {
  margin-top: 12px;
}
.mr-12.data-v-ab6b2dd0 {
  margin-right: 12px;
}
.mb-12.data-v-ab6b2dd0 {
  margin-bottom: 12px;
}
.ml-12.data-v-ab6b2dd0 {
  margin-left: 12px;
}
.mx-12.data-v-ab6b2dd0 {
  margin-left: 12px;
  margin-right: 12px;
}
.my-12.data-v-ab6b2dd0 {
  margin-top: 12px;
  margin-bottom: 12px;
}
.p-12.data-v-ab6b2dd0 {
  padding: 12px;
}
.pt-12.data-v-ab6b2dd0 {
  padding-top: 12px;
}
.pr-12.data-v-ab6b2dd0 {
  padding-right: 12px;
}
.pb-12.data-v-ab6b2dd0 {
  padding-bottom: 12px;
}
.pl-12.data-v-ab6b2dd0 {
  padding-left: 12px;
}
.px-12.data-v-ab6b2dd0 {
  padding-left: 12px;
  padding-right: 12px;
}
.py-12.data-v-ab6b2dd0 {
  padding-top: 12px;
  padding-bottom: 12px;
}
.m-16.data-v-ab6b2dd0 {
  margin: 16px;
}
.mt-16.data-v-ab6b2dd0 {
  margin-top: 16px;
}
.mr-16.data-v-ab6b2dd0 {
  margin-right: 16px;
}
.mb-16.data-v-ab6b2dd0 {
  margin-bottom: 16px;
}
.ml-16.data-v-ab6b2dd0 {
  margin-left: 16px;
}
.mx-16.data-v-ab6b2dd0 {
  margin-left: 16px;
  margin-right: 16px;
}
.my-16.data-v-ab6b2dd0 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.p-16.data-v-ab6b2dd0 {
  padding: 16px;
}
.pt-16.data-v-ab6b2dd0 {
  padding-top: 16px;
}
.pr-16.data-v-ab6b2dd0 {
  padding-right: 16px;
}
.pb-16.data-v-ab6b2dd0 {
  padding-bottom: 16px;
}
.pl-16.data-v-ab6b2dd0 {
  padding-left: 16px;
}
.px-16.data-v-ab6b2dd0 {
  padding-left: 16px;
  padding-right: 16px;
}
.py-16.data-v-ab6b2dd0 {
  padding-top: 16px;
  padding-bottom: 16px;
}
.m-20.data-v-ab6b2dd0 {
  margin: 20px;
}
.mt-20.data-v-ab6b2dd0 {
  margin-top: 20px;
}
.mr-20.data-v-ab6b2dd0 {
  margin-right: 20px;
}
.mb-20.data-v-ab6b2dd0 {
  margin-bottom: 20px;
}
.ml-20.data-v-ab6b2dd0 {
  margin-left: 20px;
}
.mx-20.data-v-ab6b2dd0 {
  margin-left: 20px;
  margin-right: 20px;
}
.my-20.data-v-ab6b2dd0 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.p-20.data-v-ab6b2dd0 {
  padding: 20px;
}
.pt-20.data-v-ab6b2dd0 {
  padding-top: 20px;
}
.pr-20.data-v-ab6b2dd0 {
  padding-right: 20px;
}
.pb-20.data-v-ab6b2dd0 {
  padding-bottom: 20px;
}
.pl-20.data-v-ab6b2dd0 {
  padding-left: 20px;
}
.px-20.data-v-ab6b2dd0 {
  padding-left: 20px;
  padding-right: 20px;
}
.py-20.data-v-ab6b2dd0 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.m-24.data-v-ab6b2dd0 {
  margin: 24px;
}
.mt-24.data-v-ab6b2dd0 {
  margin-top: 24px;
}
.mr-24.data-v-ab6b2dd0 {
  margin-right: 24px;
}
.mb-24.data-v-ab6b2dd0 {
  margin-bottom: 24px;
}
.ml-24.data-v-ab6b2dd0 {
  margin-left: 24px;
}
.mx-24.data-v-ab6b2dd0 {
  margin-left: 24px;
  margin-right: 24px;
}
.my-24.data-v-ab6b2dd0 {
  margin-top: 24px;
  margin-bottom: 24px;
}
.p-24.data-v-ab6b2dd0 {
  padding: 24px;
}
.pt-24.data-v-ab6b2dd0 {
  padding-top: 24px;
}
.pr-24.data-v-ab6b2dd0 {
  padding-right: 24px;
}
.pb-24.data-v-ab6b2dd0 {
  padding-bottom: 24px;
}
.pl-24.data-v-ab6b2dd0 {
  padding-left: 24px;
}
.px-24.data-v-ab6b2dd0 {
  padding-left: 24px;
  padding-right: 24px;
}
.py-24.data-v-ab6b2dd0 {
  padding-top: 24px;
  padding-bottom: 24px;
}
.m-32.data-v-ab6b2dd0 {
  margin: 32px;
}
.mt-32.data-v-ab6b2dd0 {
  margin-top: 32px;
}
.mr-32.data-v-ab6b2dd0 {
  margin-right: 32px;
}
.mb-32.data-v-ab6b2dd0 {
  margin-bottom: 32px;
}
.ml-32.data-v-ab6b2dd0 {
  margin-left: 32px;
}
.mx-32.data-v-ab6b2dd0 {
  margin-left: 32px;
  margin-right: 32px;
}
.my-32.data-v-ab6b2dd0 {
  margin-top: 32px;
  margin-bottom: 32px;
}
.p-32.data-v-ab6b2dd0 {
  padding: 32px;
}
.pt-32.data-v-ab6b2dd0 {
  padding-top: 32px;
}
.pr-32.data-v-ab6b2dd0 {
  padding-right: 32px;
}
.pb-32.data-v-ab6b2dd0 {
  padding-bottom: 32px;
}
.pl-32.data-v-ab6b2dd0 {
  padding-left: 32px;
}
.px-32.data-v-ab6b2dd0 {
  padding-left: 32px;
  padding-right: 32px;
}
.py-32.data-v-ab6b2dd0 {
  padding-top: 32px;
  padding-bottom: 32px;
}
.m-40.data-v-ab6b2dd0 {
  margin: 40px;
}
.mt-40.data-v-ab6b2dd0 {
  margin-top: 40px;
}
.mr-40.data-v-ab6b2dd0 {
  margin-right: 40px;
}
.mb-40.data-v-ab6b2dd0 {
  margin-bottom: 40px;
}
.ml-40.data-v-ab6b2dd0 {
  margin-left: 40px;
}
.mx-40.data-v-ab6b2dd0 {
  margin-left: 40px;
  margin-right: 40px;
}
.my-40.data-v-ab6b2dd0 {
  margin-top: 40px;
  margin-bottom: 40px;
}
.p-40.data-v-ab6b2dd0 {
  padding: 40px;
}
.pt-40.data-v-ab6b2dd0 {
  padding-top: 40px;
}
.pr-40.data-v-ab6b2dd0 {
  padding-right: 40px;
}
.pb-40.data-v-ab6b2dd0 {
  padding-bottom: 40px;
}
.pl-40.data-v-ab6b2dd0 {
  padding-left: 40px;
}
.px-40.data-v-ab6b2dd0 {
  padding-left: 40px;
  padding-right: 40px;
}
.py-40.data-v-ab6b2dd0 {
  padding-top: 40px;
  padding-bottom: 40px;
}
.m-48.data-v-ab6b2dd0 {
  margin: 48px;
}
.mt-48.data-v-ab6b2dd0 {
  margin-top: 48px;
}
.mr-48.data-v-ab6b2dd0 {
  margin-right: 48px;
}
.mb-48.data-v-ab6b2dd0 {
  margin-bottom: 48px;
}
.ml-48.data-v-ab6b2dd0 {
  margin-left: 48px;
}
.mx-48.data-v-ab6b2dd0 {
  margin-left: 48px;
  margin-right: 48px;
}
.my-48.data-v-ab6b2dd0 {
  margin-top: 48px;
  margin-bottom: 48px;
}
.p-48.data-v-ab6b2dd0 {
  padding: 48px;
}
.pt-48.data-v-ab6b2dd0 {
  padding-top: 48px;
}
.pr-48.data-v-ab6b2dd0 {
  padding-right: 48px;
}
.pb-48.data-v-ab6b2dd0 {
  padding-bottom: 48px;
}
.pl-48.data-v-ab6b2dd0 {
  padding-left: 48px;
}
.px-48.data-v-ab6b2dd0 {
  padding-left: 48px;
  padding-right: 48px;
}
.py-48.data-v-ab6b2dd0 {
  padding-top: 48px;
  padding-bottom: 48px;
}
@media (min-width: 1024px) {
.hover-lift.data-v-ab6b2dd0 {
    transition: -webkit-transform 0.2s ease;
    transition: transform 0.2s ease;
    transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.hover-lift.data-v-ab6b2dd0:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
}
.hover-scale.data-v-ab6b2dd0 {
    transition: -webkit-transform 0.2s ease;
    transition: transform 0.2s ease;
    transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.hover-scale.data-v-ab6b2dd0:hover {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
button.data-v-ab6b2dd0, .button.data-v-ab6b2dd0 {
    cursor: pointer;
    transition: all 0.2s ease;
}
button.data-v-ab6b2dd0:hover, .button.data-v-ab6b2dd0:hover {
    opacity: 0.8;
}
button.data-v-ab6b2dd0:active, .button.data-v-ab6b2dd0:active {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
}
._a.data-v-ab6b2dd0, .link.data-v-ab6b2dd0 {
    cursor: pointer;
    transition: color 0.2s ease;
}
input.data-v-ab6b2dd0, textarea.data-v-ab6b2dd0, .input.data-v-ab6b2dd0 {
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
input.data-v-ab6b2dd0:focus, textarea.data-v-ab6b2dd0:focus, .input.data-v-ab6b2dd0:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
}
@media (max-width: 1023px) {
.touch-feedback.data-v-ab6b2dd0 {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}
.touch-feedback.data-v-ab6b2dd0:active {
    background-color: rgba(0, 0, 0, 0.05);
}
button.data-v-ab6b2dd0, .button.data-v-ab6b2dd0 {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}
}
.gpu-accelerated.data-v-ab6b2dd0 {
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.smooth-scroll.data-v-ab6b2dd0 {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
@media (min-width: 1024px) {
page.data-v-ab6b2dd0, page.data-v-ab6b2dd0 {
    margin: 0;
    padding: 0;
    overflow: hidden;
}
.uni-page-head.data-v-ab6b2dd0 {
    display: none !important;
}
.uni-page-wrapper.data-v-ab6b2dd0 {
    height: 100vh !important;
}
.data-v-ab6b2dd0::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.data-v-ab6b2dd0::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}
.data-v-ab6b2dd0::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}
.data-v-ab6b2dd0::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
}
page.data-v-ab6b2dd0 {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}
.button.data-v-ab6b2dd0 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}
.button.primary.data-v-ab6b2dd0 {
  background: #1890ff;
  color: white;
}
.button.primary.data-v-ab6b2dd0:hover {
  background: #40a9ff;
}
.button.primary.data-v-ab6b2dd0:active {
  background: #096dd9;
}
.button.secondary.data-v-ab6b2dd0 {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #d9d9d9;
}
.button.secondary.data-v-ab6b2dd0:hover {
  background: #fafafa;
  border-color: #40a9ff;
}
@media (min-width: 1024px) {
page.data-v-ab6b2dd0 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}
.uni-page-wrapper.data-v-ab6b2dd0 {
    width: 375px !important;
    height: 667px !important;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
    -webkit-transform-origin: center;
            transform-origin: center;
    position: relative;
    transition: -webkit-transform 0.3s ease;
    transition: transform 0.3s ease;
    transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.uni-tabbar.data-v-ab6b2dd0 {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    border-radius: 0 0 12px 12px;
}
.uni-page-head.data-v-ab6b2dd0 {
    display: none !important;
}
}
.fade-enter-active.data-v-ab6b2dd0,
.fade-leave-active.data-v-ab6b2dd0 {
  transition: opacity 0.3s ease;
}
.fade-enter.data-v-ab6b2dd0,
.fade-leave-to.data-v-ab6b2dd0 {
  opacity: 0;
}
.slide-enter-active.data-v-ab6b2dd0,
.slide-leave-active.data-v-ab6b2dd0 {
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.slide-enter.data-v-ab6b2dd0,
.slide-leave-to.data-v-ab6b2dd0 {
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
}
/**
 * 多端适配样式系统
 * 支持PC端缩放、响应式布局和平台特定优化
 */
.multi-platform-pc.data-v-ab6b2dd0 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  margin: 0;
  padding: 20px;
  transition: all 0.3s ease;
}
.multi-platform-pc .uni-app.data-v-ab6b2dd0,
.multi-platform-pc #app.data-v-ab6b2dd0 {
  width: 375px !important;
  height: 667px !important;
  max-width: 375px !important;
  max-height: 667px !important;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
  -webkit-transform-origin: center;
          transform-origin: center;
  position: relative;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
}
.multi-platform-pc .uni-page-wrapper.data-v-ab6b2dd0 {
  border-radius: 12px;
  overflow: hidden;
  height: 100% !important;
}
.multi-platform-pc .uni-tabbar.data-v-ab6b2dd0 {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  border-radius: 0 0 12px 12px;
}
@media (max-width: 1023px) {
.multi-platform-pc.data-v-ab6b2dd0 {
    background: #f5f5f5;
    padding: 0;
    display: block;
}
.multi-platform-pc .uni-app.data-v-ab6b2dd0,
  .multi-platform-pc #app.data-v-ab6b2dd0 {
    width: 100% !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 0;
    box-shadow: none;
    -webkit-transform: none;
            transform: none;
}
.multi-platform-pc .uni-page-wrapper.data-v-ab6b2dd0 {
    border-radius: 0;
}
.multi-platform-pc .uni-tabbar.data-v-ab6b2dd0 {
    position: fixed !important;
}
}
.responsive-container.data-v-ab6b2dd0, .container.data-v-ab6b2dd0 {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-container.data-v-ab6b2dd0, .container.data-v-ab6b2dd0 {
    max-width: 750px;
    padding: 0 24px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-container.data-v-ab6b2dd0, .container.data-v-ab6b2dd0 {
    max-width: 1000px;
    padding: 0 32px;
}
}
@media (min-width: 1440px) {
.responsive-container.data-v-ab6b2dd0, .container.data-v-ab6b2dd0 {
    max-width: 1200px;
    padding: 0 40px;
}
}
.responsive-grid.data-v-ab6b2dd0 {
  display: grid;
  gap: 16px;
  grid-template-columns: 1fr;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-grid.data-v-ab6b2dd0 {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-grid.data-v-ab6b2dd0 {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
}
}
@media (min-width: 1440px) {
.responsive-grid.data-v-ab6b2dd0 {
    grid-template-columns: repeat(4, 1fr);
    gap: 28px;
}
}
.responsive-card.data-v-ab6b2dd0, .card.data-v-ab6b2dd0 {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-card.data-v-ab6b2dd0, .card.data-v-ab6b2dd0 {
    padding: 20px;
    border-radius: 10px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-card.data-v-ab6b2dd0, .card.data-v-ab6b2dd0 {
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
.responsive-card.data-v-ab6b2dd0:hover, .card.data-v-ab6b2dd0:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}
}
.platform-h5.platform-h5-pc .uni-page-head.data-v-ab6b2dd0 {
  display: none !important;
}
.show-mobile.data-v-ab6b2dd0 {
  display: block;
}
@media (min-width: 768px) and (max-width: 1023px) {
.show-mobile.data-v-ab6b2dd0 {
    display: none;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.show-mobile.data-v-ab6b2dd0 {
    display: none;
}
}
@media (min-width: 1440px) {
.show-mobile.data-v-ab6b2dd0 {
    display: none;
}
}
.show-tablet.data-v-ab6b2dd0 {
  display: none;
}
@media (min-width: 768px) and (max-width: 1023px) {
.show-tablet.data-v-ab6b2dd0 {
    display: block;
}
}
.show-desktop.data-v-ab6b2dd0 {
  display: none;
}
@media (min-width: 1024px) and (max-width: 1439px) {
.show-desktop.data-v-ab6b2dd0 {
    display: block;
}
}
.show-large.data-v-ab6b2dd0 {
  display: none;
}
@media (min-width: 1440px) {
.show-large.data-v-ab6b2dd0 {
    display: block;
}
}
@media (min-width: 0px) and (max-width: 767px) {
.hide-mobile.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (min-width: 768px) and (max-width: 1023px) {
.hide-tablet.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.hide-desktop.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (min-width: 1440px) {
.hide-large.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (max-width: 1023px) {
.pc-only.data-v-ab6b2dd0 {
    display: none !important;
}
}
@media (min-width: 1024px) {
.mobile-only.data-v-ab6b2dd0 {
    display: none !important;
}
}
.platform-show-h5.data-v-ab6b2dd0 {
  display: none;
}
.platform-h5 .platform-show-h5.data-v-ab6b2dd0 {
  display: block;
}
.platform-show-app.data-v-ab6b2dd0 {
  display: none;
}
.platform-app .platform-show-app.data-v-ab6b2dd0 {
  display: block;
}
.platform-show-mp.data-v-ab6b2dd0 {
  display: none;
}
.platform-mp-weixin .platform-show-mp.data-v-ab6b2dd0 {
  display: block;
}
.platform-mp-alipay .platform-show-mp.data-v-ab6b2dd0 {
  display: block;
}
.platform-mp-baidu .platform-show-mp.data-v-ab6b2dd0 {
  display: block;
}
.flex.data-v-ab6b2dd0 {
  display: flex;
}
.flex-column.data-v-ab6b2dd0 {
  flex-direction: column;
}
.flex-row.data-v-ab6b2dd0 {
  flex-direction: row;
}
.flex-wrap.data-v-ab6b2dd0 {
  flex-wrap: wrap;
}
.justify-center.data-v-ab6b2dd0 {
  justify-content: center;
}
.justify-between.data-v-ab6b2dd0 {
  justify-content: space-between;
}
.justify-around.data-v-ab6b2dd0 {
  justify-content: space-around;
}
.align-center.data-v-ab6b2dd0 {
  align-items: center;
}
.align-start.data-v-ab6b2dd0 {
  align-items: flex-start;
}
.align-end.data-v-ab6b2dd0 {
  align-items: flex-end;
}
.text-left.data-v-ab6b2dd0 {
  text-align: left;
}
.text-center.data-v-ab6b2dd0 {
  text-align: center;
}
.text-right.data-v-ab6b2dd0 {
  text-align: right;
}
.m-0.data-v-ab6b2dd0 {
  margin: 0px;
}
.mt-0.data-v-ab6b2dd0 {
  margin-top: 0px;
}
.mr-0.data-v-ab6b2dd0 {
  margin-right: 0px;
}
.mb-0.data-v-ab6b2dd0 {
  margin-bottom: 0px;
}
.ml-0.data-v-ab6b2dd0 {
  margin-left: 0px;
}
.mx-0.data-v-ab6b2dd0 {
  margin-left: 0px;
  margin-right: 0px;
}
.my-0.data-v-ab6b2dd0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.p-0.data-v-ab6b2dd0 {
  padding: 0px;
}
.pt-0.data-v-ab6b2dd0 {
  padding-top: 0px;
}
.pr-0.data-v-ab6b2dd0 {
  padding-right: 0px;
}
.pb-0.data-v-ab6b2dd0 {
  padding-bottom: 0px;
}
.pl-0.data-v-ab6b2dd0 {
  padding-left: 0px;
}
.px-0.data-v-ab6b2dd0 {
  padding-left: 0px;
  padding-right: 0px;
}
.py-0.data-v-ab6b2dd0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.m-4.data-v-ab6b2dd0 {
  margin: 4px;
}
.mt-4.data-v-ab6b2dd0 {
  margin-top: 4px;
}
.mr-4.data-v-ab6b2dd0 {
  margin-right: 4px;
}
.mb-4.data-v-ab6b2dd0 {
  margin-bottom: 4px;
}
.ml-4.data-v-ab6b2dd0 {
  margin-left: 4px;
}
.mx-4.data-v-ab6b2dd0 {
  margin-left: 4px;
  margin-right: 4px;
}
.my-4.data-v-ab6b2dd0 {
  margin-top: 4px;
  margin-bottom: 4px;
}
.p-4.data-v-ab6b2dd0 {
  padding: 4px;
}
.pt-4.data-v-ab6b2dd0 {
  padding-top: 4px;
}
.pr-4.data-v-ab6b2dd0 {
  padding-right: 4px;
}
.pb-4.data-v-ab6b2dd0 {
  padding-bottom: 4px;
}
.pl-4.data-v-ab6b2dd0 {
  padding-left: 4px;
}
.px-4.data-v-ab6b2dd0 {
  padding-left: 4px;
  padding-right: 4px;
}
.py-4.data-v-ab6b2dd0 {
  padding-top: 4px;
  padding-bottom: 4px;
}
.m-8.data-v-ab6b2dd0 {
  margin: 8px;
}
.mt-8.data-v-ab6b2dd0 {
  margin-top: 8px;
}
.mr-8.data-v-ab6b2dd0 {
  margin-right: 8px;
}
.mb-8.data-v-ab6b2dd0 {
  margin-bottom: 8px;
}
.ml-8.data-v-ab6b2dd0 {
  margin-left: 8px;
}
.mx-8.data-v-ab6b2dd0 {
  margin-left: 8px;
  margin-right: 8px;
}
.my-8.data-v-ab6b2dd0 {
  margin-top: 8px;
  margin-bottom: 8px;
}
.p-8.data-v-ab6b2dd0 {
  padding: 8px;
}
.pt-8.data-v-ab6b2dd0 {
  padding-top: 8px;
}
.pr-8.data-v-ab6b2dd0 {
  padding-right: 8px;
}
.pb-8.data-v-ab6b2dd0 {
  padding-bottom: 8px;
}
.pl-8.data-v-ab6b2dd0 {
  padding-left: 8px;
}
.px-8.data-v-ab6b2dd0 {
  padding-left: 8px;
  padding-right: 8px;
}
.py-8.data-v-ab6b2dd0 {
  padding-top: 8px;
  padding-bottom: 8px;
}
.m-12.data-v-ab6b2dd0 {
  margin: 12px;
}
.mt-12.data-v-ab6b2dd0 {
  margin-top: 12px;
}
.mr-12.data-v-ab6b2dd0 {
  margin-right: 12px;
}
.mb-12.data-v-ab6b2dd0 {
  margin-bottom: 12px;
}
.ml-12.data-v-ab6b2dd0 {
  margin-left: 12px;
}
.mx-12.data-v-ab6b2dd0 {
  margin-left: 12px;
  margin-right: 12px;
}
.my-12.data-v-ab6b2dd0 {
  margin-top: 12px;
  margin-bottom: 12px;
}
.p-12.data-v-ab6b2dd0 {
  padding: 12px;
}
.pt-12.data-v-ab6b2dd0 {
  padding-top: 12px;
}
.pr-12.data-v-ab6b2dd0 {
  padding-right: 12px;
}
.pb-12.data-v-ab6b2dd0 {
  padding-bottom: 12px;
}
.pl-12.data-v-ab6b2dd0 {
  padding-left: 12px;
}
.px-12.data-v-ab6b2dd0 {
  padding-left: 12px;
  padding-right: 12px;
}
.py-12.data-v-ab6b2dd0 {
  padding-top: 12px;
  padding-bottom: 12px;
}
.m-16.data-v-ab6b2dd0 {
  margin: 16px;
}
.mt-16.data-v-ab6b2dd0 {
  margin-top: 16px;
}
.mr-16.data-v-ab6b2dd0 {
  margin-right: 16px;
}
.mb-16.data-v-ab6b2dd0 {
  margin-bottom: 16px;
}
.ml-16.data-v-ab6b2dd0 {
  margin-left: 16px;
}
.mx-16.data-v-ab6b2dd0 {
  margin-left: 16px;
  margin-right: 16px;
}
.my-16.data-v-ab6b2dd0 {
  margin-top: 16px;
  margin-bottom: 16px;
}
.p-16.data-v-ab6b2dd0 {
  padding: 16px;
}
.pt-16.data-v-ab6b2dd0 {
  padding-top: 16px;
}
.pr-16.data-v-ab6b2dd0 {
  padding-right: 16px;
}
.pb-16.data-v-ab6b2dd0 {
  padding-bottom: 16px;
}
.pl-16.data-v-ab6b2dd0 {
  padding-left: 16px;
}
.px-16.data-v-ab6b2dd0 {
  padding-left: 16px;
  padding-right: 16px;
}
.py-16.data-v-ab6b2dd0 {
  padding-top: 16px;
  padding-bottom: 16px;
}
.m-20.data-v-ab6b2dd0 {
  margin: 20px;
}
.mt-20.data-v-ab6b2dd0 {
  margin-top: 20px;
}
.mr-20.data-v-ab6b2dd0 {
  margin-right: 20px;
}
.mb-20.data-v-ab6b2dd0 {
  margin-bottom: 20px;
}
.ml-20.data-v-ab6b2dd0 {
  margin-left: 20px;
}
.mx-20.data-v-ab6b2dd0 {
  margin-left: 20px;
  margin-right: 20px;
}
.my-20.data-v-ab6b2dd0 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.p-20.data-v-ab6b2dd0 {
  padding: 20px;
}
.pt-20.data-v-ab6b2dd0 {
  padding-top: 20px;
}
.pr-20.data-v-ab6b2dd0 {
  padding-right: 20px;
}
.pb-20.data-v-ab6b2dd0 {
  padding-bottom: 20px;
}
.pl-20.data-v-ab6b2dd0 {
  padding-left: 20px;
}
.px-20.data-v-ab6b2dd0 {
  padding-left: 20px;
  padding-right: 20px;
}
.py-20.data-v-ab6b2dd0 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.m-24.data-v-ab6b2dd0 {
  margin: 24px;
}
.mt-24.data-v-ab6b2dd0 {
  margin-top: 24px;
}
.mr-24.data-v-ab6b2dd0 {
  margin-right: 24px;
}
.mb-24.data-v-ab6b2dd0 {
  margin-bottom: 24px;
}
.ml-24.data-v-ab6b2dd0 {
  margin-left: 24px;
}
.mx-24.data-v-ab6b2dd0 {
  margin-left: 24px;
  margin-right: 24px;
}
.my-24.data-v-ab6b2dd0 {
  margin-top: 24px;
  margin-bottom: 24px;
}
.p-24.data-v-ab6b2dd0 {
  padding: 24px;
}
.pt-24.data-v-ab6b2dd0 {
  padding-top: 24px;
}
.pr-24.data-v-ab6b2dd0 {
  padding-right: 24px;
}
.pb-24.data-v-ab6b2dd0 {
  padding-bottom: 24px;
}
.pl-24.data-v-ab6b2dd0 {
  padding-left: 24px;
}
.px-24.data-v-ab6b2dd0 {
  padding-left: 24px;
  padding-right: 24px;
}
.py-24.data-v-ab6b2dd0 {
  padding-top: 24px;
  padding-bottom: 24px;
}
.m-32.data-v-ab6b2dd0 {
  margin: 32px;
}
.mt-32.data-v-ab6b2dd0 {
  margin-top: 32px;
}
.mr-32.data-v-ab6b2dd0 {
  margin-right: 32px;
}
.mb-32.data-v-ab6b2dd0 {
  margin-bottom: 32px;
}
.ml-32.data-v-ab6b2dd0 {
  margin-left: 32px;
}
.mx-32.data-v-ab6b2dd0 {
  margin-left: 32px;
  margin-right: 32px;
}
.my-32.data-v-ab6b2dd0 {
  margin-top: 32px;
  margin-bottom: 32px;
}
.p-32.data-v-ab6b2dd0 {
  padding: 32px;
}
.pt-32.data-v-ab6b2dd0 {
  padding-top: 32px;
}
.pr-32.data-v-ab6b2dd0 {
  padding-right: 32px;
}
.pb-32.data-v-ab6b2dd0 {
  padding-bottom: 32px;
}
.pl-32.data-v-ab6b2dd0 {
  padding-left: 32px;
}
.px-32.data-v-ab6b2dd0 {
  padding-left: 32px;
  padding-right: 32px;
}
.py-32.data-v-ab6b2dd0 {
  padding-top: 32px;
  padding-bottom: 32px;
}
.m-40.data-v-ab6b2dd0 {
  margin: 40px;
}
.mt-40.data-v-ab6b2dd0 {
  margin-top: 40px;
}
.mr-40.data-v-ab6b2dd0 {
  margin-right: 40px;
}
.mb-40.data-v-ab6b2dd0 {
  margin-bottom: 40px;
}
.ml-40.data-v-ab6b2dd0 {
  margin-left: 40px;
}
.mx-40.data-v-ab6b2dd0 {
  margin-left: 40px;
  margin-right: 40px;
}
.my-40.data-v-ab6b2dd0 {
  margin-top: 40px;
  margin-bottom: 40px;
}
.p-40.data-v-ab6b2dd0 {
  padding: 40px;
}
.pt-40.data-v-ab6b2dd0 {
  padding-top: 40px;
}
.pr-40.data-v-ab6b2dd0 {
  padding-right: 40px;
}
.pb-40.data-v-ab6b2dd0 {
  padding-bottom: 40px;
}
.pl-40.data-v-ab6b2dd0 {
  padding-left: 40px;
}
.px-40.data-v-ab6b2dd0 {
  padding-left: 40px;
  padding-right: 40px;
}
.py-40.data-v-ab6b2dd0 {
  padding-top: 40px;
  padding-bottom: 40px;
}
.m-48.data-v-ab6b2dd0 {
  margin: 48px;
}
.mt-48.data-v-ab6b2dd0 {
  margin-top: 48px;
}
.mr-48.data-v-ab6b2dd0 {
  margin-right: 48px;
}
.mb-48.data-v-ab6b2dd0 {
  margin-bottom: 48px;
}
.ml-48.data-v-ab6b2dd0 {
  margin-left: 48px;
}
.mx-48.data-v-ab6b2dd0 {
  margin-left: 48px;
  margin-right: 48px;
}
.my-48.data-v-ab6b2dd0 {
  margin-top: 48px;
  margin-bottom: 48px;
}
.p-48.data-v-ab6b2dd0 {
  padding: 48px;
}
.pt-48.data-v-ab6b2dd0 {
  padding-top: 48px;
}
.pr-48.data-v-ab6b2dd0 {
  padding-right: 48px;
}
.pb-48.data-v-ab6b2dd0 {
  padding-bottom: 48px;
}
.pl-48.data-v-ab6b2dd0 {
  padding-left: 48px;
}
.px-48.data-v-ab6b2dd0 {
  padding-left: 48px;
  padding-right: 48px;
}
.py-48.data-v-ab6b2dd0 {
  padding-top: 48px;
  padding-bottom: 48px;
}
@media (min-width: 1024px) {
.hover-lift.data-v-ab6b2dd0 {
    transition: -webkit-transform 0.2s ease;
    transition: transform 0.2s ease;
    transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.hover-lift.data-v-ab6b2dd0:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
}
.hover-scale.data-v-ab6b2dd0 {
    transition: -webkit-transform 0.2s ease;
    transition: transform 0.2s ease;
    transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.hover-scale.data-v-ab6b2dd0:hover {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
button.data-v-ab6b2dd0, .button.data-v-ab6b2dd0 {
    cursor: pointer;
    transition: all 0.2s ease;
}
button.data-v-ab6b2dd0:hover, .button.data-v-ab6b2dd0:hover {
    opacity: 0.8;
}
button.data-v-ab6b2dd0:active, .button.data-v-ab6b2dd0:active {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
}
._a.data-v-ab6b2dd0, .link.data-v-ab6b2dd0 {
    cursor: pointer;
    transition: color 0.2s ease;
}
input.data-v-ab6b2dd0, textarea.data-v-ab6b2dd0, .input.data-v-ab6b2dd0 {
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
input.data-v-ab6b2dd0:focus, textarea.data-v-ab6b2dd0:focus, .input.data-v-ab6b2dd0:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
}
@media (max-width: 1023px) {
.touch-feedback.data-v-ab6b2dd0 {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}
.touch-feedback.data-v-ab6b2dd0:active {
    background-color: rgba(0, 0, 0, 0.05);
}
button.data-v-ab6b2dd0, .button.data-v-ab6b2dd0 {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}
}
.gpu-accelerated.data-v-ab6b2dd0 {
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  will-change: transform;
}
.smooth-scroll.data-v-ab6b2dd0 {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
@media (min-width: 1024px) {
page.data-v-ab6b2dd0, page.data-v-ab6b2dd0 {
    margin: 0;
    padding: 0;
    overflow: hidden;
}
.uni-page-head.data-v-ab6b2dd0 {
    display: none !important;
}
.uni-page-wrapper.data-v-ab6b2dd0 {
    height: 100vh !important;
}
.data-v-ab6b2dd0::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
.data-v-ab6b2dd0::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}
.data-v-ab6b2dd0::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}
.data-v-ab6b2dd0::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
}
.responsive-card.data-v-ab6b2dd0, .card.data-v-ab6b2dd0 {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}
.responsive-card__header.data-v-ab6b2dd0 {
  padding: 16px 16px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}
.responsive-card__title.data-v-ab6b2dd0 {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}
.responsive-card__subtitle.data-v-ab6b2dd0 {
  display: block;
  font-size: 14px;
  color: #666;
}
.responsive-card__body.data-v-ab6b2dd0 {
  padding: 16px;
}
.responsive-card__footer.data-v-ab6b2dd0 {
  padding: 0 16px 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}
.responsive-card--clickable.data-v-ab6b2dd0 {
  cursor: pointer;
}
.responsive-card--clickable.data-v-ab6b2dd0:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
@media (min-width: 1024px) {
.responsive-card--hoverable.data-v-ab6b2dd0:hover {
    -webkit-transform: translateY(-2px);
            transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}
}
@media (min-width: 1024px) {
.responsive-card--pc-optimized.data-v-ab6b2dd0 {
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
}
.responsive-card--shadow-sm.data-v-ab6b2dd0 {
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
.responsive-card--shadow-md.data-v-ab6b2dd0 {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.responsive-card--shadow-lg.data-v-ab6b2dd0 {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}
.responsive-card--shadow-xl.data-v-ab6b2dd0 {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}
@media (min-width: 0px) and (max-width: 767px) {
.responsive-card--mobile.data-v-ab6b2dd0 {
    border-radius: 8px;
}
.responsive-card--mobile .responsive-card__header.data-v-ab6b2dd0,
  .responsive-card--mobile .responsive-card__body.data-v-ab6b2dd0,
  .responsive-card--mobile .responsive-card__footer.data-v-ab6b2dd0 {
    padding-left: 16px;
    padding-right: 16px;
}
}
@media (min-width: 768px) and (max-width: 1023px) {
.responsive-card--tablet.data-v-ab6b2dd0 {
    border-radius: 10px;
}
.responsive-card--tablet .responsive-card__header.data-v-ab6b2dd0,
  .responsive-card--tablet .responsive-card__body.data-v-ab6b2dd0,
  .responsive-card--tablet .responsive-card__footer.data-v-ab6b2dd0 {
    padding-left: 20px;
    padding-right: 20px;
}
}
@media (min-width: 1024px) and (max-width: 1439px) {
.responsive-card--desktop.data-v-ab6b2dd0 {
    border-radius: 12px;
}
.responsive-card--desktop .responsive-card__header.data-v-ab6b2dd0,
  .responsive-card--desktop .responsive-card__body.data-v-ab6b2dd0,
  .responsive-card--desktop .responsive-card__footer.data-v-ab6b2dd0 {
    padding-left: 24px;
    padding-right: 24px;
}
}
@media (min-width: 1440px) {
.responsive-card--large.data-v-ab6b2dd0 {
    border-radius: 14px;
}
.responsive-card--large .responsive-card__header.data-v-ab6b2dd0,
  .responsive-card--large .responsive-card__body.data-v-ab6b2dd0,
  .responsive-card--large .responsive-card__footer.data-v-ab6b2dd0 {
    padding-left: 28px;
    padding-right: 28px;
}
}

