<template>
  <view class="responsive-layout" :class="layoutClasses" :style="layoutStyles">
    <slot></slot>
  </view>
</template>

<script>
import multiPlatformAdapter from '@/common/js/pc-adapter.js'

export default {
  name: 'ResponsiveLayout',
  props: {
    // 布局类型：container(容器), grid(栅格), flex(弹性), row(行), col(列)
    type: {
      type: String,
      default: 'container',
      validator: value => ['container', 'grid', 'flex', 'row', 'col'].includes(value)
    },

    // 响应式配置
    responsive: {
      type: Object,
      default: () => ({})
    },

    // 最大宽度配置
    maxWidth: {
      type: [String, Number, Object],
      default: null
    },

    // 内边距配置
    padding: {
      type: [String, Number, Object],
      default: null
    },

    // 外边距配置
    margin: {
      type: [String, Number, Object],
      default: null
    },

    // 栅格列数配置
    cols: {
      type: [Number, Object],
      default: () => ({
        mobile: 1,
        tablet: 2,
        desktop: 3,
        large: 4
      })
    },

    // 间距配置
    gap: {
      type: [String, Number, Object],
      default: () => ({
        mobile: '12px',
        tablet: '16px',
        desktop: '20px',
        large: '24px'
      })
    },

    // 对齐方式
    align: {
      type: [String, Object],
      default: 'stretch'
    },

    // 水平对齐
    justify: {
      type: [String, Object],
      default: 'start'
    },

    // 显示/隐藏控制
    hidden: {
      type: [String, Array],
      default: null
    },

    visible: {
      type: [String, Array],
      default: null
    },

    // 是否启用PC端优化
    pcOptimized: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      adapterInfo: {
        platform: {},
        device: {},
        screenType: 'mobile'
      }
    }
  },

  computed: {
    layoutClasses() {
      const classes = ['responsive-layout', 'responsive-layout--' + this.type]

      // 添加屏幕类型类
      classes.push('responsive-layout--' + this.adapterInfo.device.screenType)

      // 添加平台类
      if (this.adapterInfo.platform.current) {
        classes.push('responsive-layout--platform-' + this.adapterInfo.platform.current)
      }

      // 添加设备类型类
      if (this.adapterInfo.platform.isPC) {
        classes.push('responsive-layout--pc')
      }

      if (this.adapterInfo.device.isMobile) {
        classes.push('responsive-layout--mobile-device')
      }

      // 添加对齐类
      const align = this.getResponsiveValue(this.align)
      if (align) {
        classes.push('responsive-layout--align-' + align)
      }

      const justify = this.getResponsiveValue(this.justify)
      if (justify) {
        classes.push('responsive-layout--justify-' + justify)
      }

      // 处理显示/隐藏
      if (this.shouldHide()) {
        classes.push('responsive-layout--hidden')
      }

      // PC优化类
      if (this.pcOptimized && this.adapterInfo.platform.isPC) {
        classes.push('responsive-layout--pc-optimized')
      }

      return classes
    },
    
    layoutStyles() {
      const styles = {}

      // 最大宽度
      const maxWidth = this.getResponsiveValue(this.maxWidth)
      if (maxWidth) {
        styles.maxWidth = this.formatValue(maxWidth)
      }

      // 内边距
      const padding = this.getResponsiveValue(this.padding)
      if (padding) {
        styles.padding = this.formatValue(padding)
      }

      // 外边距
      const margin = this.getResponsiveValue(this.margin)
      if (margin) {
        styles.margin = this.formatValue(margin)
      }

      // 间距
      const gap = this.getResponsiveValue(this.gap)
      if (gap) {
        styles.gap = this.formatValue(gap)
      }

      // 栅格布局
      if (this.type === 'grid') {
        const cols = this.getResponsiveValue(this.cols)
        if (cols && cols > 0) {
          styles.gridTemplateColumns = `repeat(${cols}, 1fr)`
        }
      }

      // 响应式配置
      if (this.responsive) {
        const responsiveStyles = this.getResponsiveValue(this.responsive)
        if (responsiveStyles && typeof responsiveStyles === 'object') {
          Object.assign(styles, responsiveStyles)
        }
      }

      return styles
    }
  },

  mounted() {
    this.updateAdapterInfo()
    this.setupEventListeners()
  },

  beforeDestroy() {
    this.removeEventListeners()
  },

  methods: {
    updateAdapterInfo() {
      this.adapterInfo = multiPlatformAdapter.getAdapterInfo()
    },

    setupEventListeners() {
      this.resizeHandler = () => {
        this.updateAdapterInfo()
      }

      multiPlatformAdapter.on('resize', this.resizeHandler)
    },

    removeEventListeners() {
      if (this.resizeHandler) {
        multiPlatformAdapter.off('resize', this.resizeHandler)
      }
    },

    getResponsiveValue(value) {
      return multiPlatformAdapter.getResponsiveValue(value)
    },

    formatValue(value) {
      if (typeof value === 'number') {
        return `${value}px`
      }
      return value
    },

    shouldHide() {
      const currentScreenType = this.adapterInfo.device.screenType

      if (this.hidden) {
        const hiddenTypes = Array.isArray(this.hidden) ? this.hidden : [this.hidden]
        if (hiddenTypes.includes(currentScreenType)) {
          return true
        }
      }

      if (this.visible) {
        const visibleTypes = Array.isArray(this.visible) ? this.visible : [this.visible]
        if (!visibleTypes.includes(currentScreenType)) {
          return true
        }
      }

      return false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/pc-responsive.scss';

.responsive-layout {
  box-sizing: border-box;
  transition: all 0.3s ease;

  // 基础布局类型
  &--container {
    width: 100%;
    margin: 0 auto;
    @extend .responsive-container;
  }

  &--grid {
    display: grid;
    @extend .responsive-grid;
  }

  &--flex {
    display: flex;
    flex-wrap: wrap;
  }

  &--row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }

  &--col {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;
  }

  // 对齐方式
  &--align-start { align-items: flex-start; }
  &--align-center { align-items: center; }
  &--align-end { align-items: flex-end; }
  &--align-stretch { align-items: stretch; }

  &--justify-start { justify-content: flex-start; }
  &--justify-center { justify-content: center; }
  &--justify-end { justify-content: flex-end; }
  &--justify-between { justify-content: space-between; }
  &--justify-around { justify-content: space-around; }
  &--justify-evenly { justify-content: space-evenly; }

  // 平台特定样式
  &--platform-h5 {
    // H5特定样式
  }

  &--platform-app {
    // App特定样式
  }

  &--platform-mp-weixin {
    // 微信小程序特定样式
  }

  // PC端优化
  &--pc-optimized {
    @include pc-only {
      &.responsive-layout--container {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        background: #fff;
      }

      &:hover {
        @extend .hover-lift;
      }
    }
  }

  // 屏幕类型样式
  &--mobile {
    // 移动端样式
  }

  &--tablet {
    // 平板端样式
  }

  &--desktop {
    // 桌面端样式
  }

  &--large {
    // 大屏样式
  }

  // 隐藏控制
  &--hidden {
    display: none !important;
  }
}

// 响应式容器最大宽度
.responsive-layout--container {
  @include respond-to(mobile) {
    max-width: 100%;
    padding: 0 16px;
  }

  @include respond-to(tablet) {
    max-width: 750px;
    padding: 0 24px;
  }

  @include respond-to(desktop) {
    max-width: 1000px;
    padding: 0 32px;
  }

  @include respond-to(large) {
    max-width: 1200px;
    padding: 0 40px;
  }
}
</style>
