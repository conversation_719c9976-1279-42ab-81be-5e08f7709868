<template>
  <view class="responsive-layout" :class="layoutClasses" :style="layoutStyles">
    <slot></slot>
  </view>
</template>

<script>
import pcAdapter from '@/common/js/pc-adapter.js'

export default {
  name: 'ResponsiveLayout',
  props: {
    // 布局类型：container(容器), row(行), col(列)
    type: {
      type: String,
      default: 'container'
    },
    // 最大宽度
    maxWidth: {
      type: [String, Number, Object],
      default: null
    },
    // 内边距
    padding: {
      type: [String, Number, Object],
      default: null
    },
    // 外边距
    margin: {
      type: [String, Number, Object],
      default: null
    },
    // 列数配置（用于栅格布局）
    cols: {
      type: [Number, Object],
      default: 1
    },
    // 间距
    gap: {
      type: [String, Number, Object],
      default: null
    },
    // 对齐方式
    align: {
      type: String,
      default: 'start'
    },
    // 垂直对齐
    justify: {
      type: String,
      default: 'start'
    },
    // 是否隐藏在特定断点
    hidden: {
      type: [String, Array],
      default: null
    },
    // 是否只在特定断点显示
    visible: {
      type: [String, Array],
      default: null
    }
  },
  data() {
    return {
      currentBreakpoint: 'mobile',
      windowInfo: null
    }
  },
  computed: {
    layoutClasses() {
      const classes = [`responsive-layout--${this.type}`]
      
      // 添加断点类
      classes.push(`responsive-layout--${this.currentBreakpoint}`)
      
      // 添加对齐类
      if (this.align) {
        classes.push(`responsive-layout--align-${this.align}`)
      }
      
      if (this.justify) {
        classes.push(`responsive-layout--justify-${this.justify}`)
      }
      
      // 处理隐藏/显示
      if (this.shouldHide()) {
        classes.push('responsive-layout--hidden')
      }
      
      return classes
    },
    
    layoutStyles() {
      const styles = {}
      
      // 最大宽度
      const maxWidth = this.getResponsiveValue(this.maxWidth)
      if (maxWidth) {
        styles.maxWidth = typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth
      }
      
      // 内边距
      const padding = this.getResponsiveValue(this.padding)
      if (padding) {
        styles.padding = typeof padding === 'number' ? `${padding}px` : padding
      }
      
      // 外边距
      const margin = this.getResponsiveValue(this.margin)
      if (margin) {
        styles.margin = typeof margin === 'number' ? `${margin}px` : margin
      }
      
      // 间距（用于flex布局）
      const gap = this.getResponsiveValue(this.gap)
      if (gap) {
        styles.gap = typeof gap === 'number' ? `${gap}px` : gap
      }
      
      // 栅格列数
      if (this.type === 'row') {
        const cols = this.getResponsiveValue(this.cols)
        if (cols > 1) {
          styles.gridTemplateColumns = `repeat(${cols}, 1fr)`
        }
      }
      
      return styles
    }
  },
  mounted() {
    this.updateDeviceInfo()
    this.setupResizeListener()
  },
  beforeDestroy() {
    this.removeResizeListener()
  },
  methods: {
    updateDeviceInfo() {
      const deviceInfo = pcAdapter.getDeviceInfo()
      this.currentBreakpoint = deviceInfo.breakpoint
      this.windowInfo = deviceInfo.windowInfo
    },
    
    setupResizeListener() {
      this.resizeHandler = (data) => {
        this.currentBreakpoint = data.breakpoint
        this.windowInfo = data.size || data.windowInfo
      }
      pcAdapter.onWindowResize(this.resizeHandler)
    },
    
    removeResizeListener() {
      if (this.resizeHandler) {
        pcAdapter.offWindowResize(this.resizeHandler)
      }
    },
    
    getResponsiveValue(value) {
      return pcAdapter.getResponsiveValue(value)
    },
    
    shouldHide() {
      if (this.hidden) {
        const hiddenBreakpoints = Array.isArray(this.hidden) ? this.hidden : [this.hidden]
        if (hiddenBreakpoints.includes(this.currentBreakpoint)) {
          return true
        }
      }
      
      if (this.visible) {
        const visibleBreakpoints = Array.isArray(this.visible) ? this.visible : [this.visible]
        if (!visibleBreakpoints.includes(this.currentBreakpoint)) {
          return true
        }
      }
      
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-layout {
  box-sizing: border-box;
  
  &--container {
    width: 100%;
    margin: 0 auto;
  }
  
  &--row {
    display: flex;
    flex-wrap: wrap;
    
    &.responsive-layout--desktop,
    &.responsive-layout--large {
      display: grid;
    }
  }
  
  &--col {
    flex: 1;
    min-width: 0;
  }
  
  // 对齐方式
  &--align-start { align-items: flex-start; }
  &--align-center { align-items: center; }
  &--align-end { align-items: flex-end; }
  &--align-stretch { align-items: stretch; }
  
  &--justify-start { justify-content: flex-start; }
  &--justify-center { justify-content: center; }
  &--justify-end { justify-content: flex-end; }
  &--justify-between { justify-content: space-between; }
  &--justify-around { justify-content: space-around; }
  
  &--hidden {
    display: none !important;
  }
}

// 响应式断点样式
@media (min-width: 768px) {
  .responsive-layout--container {
    max-width: 750px;
  }
}

@media (min-width: 1024px) {
  .responsive-layout--container {
    max-width: 1000px;
  }
}

@media (min-width: 1440px) {
  .responsive-layout--container {
    max-width: 1200px;
  }
}
</style>
