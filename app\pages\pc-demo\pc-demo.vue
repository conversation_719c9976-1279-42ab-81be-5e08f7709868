<template>
  <view class="pc-demo">
    <!-- 顶部标题 -->
    <view class="header">
      <text class="title">PC端适配演示</text>
      <text class="subtitle">在PC端自动缩放，保持移动端体验</text>
    </view>

    <!-- 设备状态 -->
    <view class="status-card">
      <view class="status-item">
        <text class="status-label">设备类型：</text>
        <text class="status-value" :class="{ 'pc-device': isPC }">
          {{ isPC ? 'PC端' : '移动端' }}
        </text>
      </view>
      <view class="status-item" v-if="isPC">
        <text class="status-label">缩放比例：</text>
        <text class="status-value">{{ scaleRatio }}</text>
      </view>
      <view class="status-item">
        <text class="status-label">窗口大小：</text>
        <text class="status-value">{{ windowSize.width }}x{{ windowSize.height }}</text>
      </view>
    </view>

    <!-- 功能演示区域 -->
    <view class="demo-section">
      <view class="section-title">功能演示</view>
      
      <!-- 卡片列表 -->
      <view class="card-list">
        <view class="demo-card" v-for="(item, index) in demoItems" :key="index">
          <view class="card-icon">{{ item.icon }}</view>
          <view class="card-content">
            <text class="card-title">{{ item.title }}</text>
            <text class="card-desc">{{ item.desc }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 交互演示 -->
    <view class="interaction-section">
      <view class="section-title">交互演示</view>
      
      <view class="button-grid">
        <button class="demo-btn primary" @click="showMessage('主要按钮')">主要按钮</button>
        <button class="demo-btn secondary" @click="showMessage('次要按钮')">次要按钮</button>
        <button class="demo-btn success" @click="showMessage('成功按钮')">成功按钮</button>
        <button class="demo-btn warning" @click="showMessage('警告按钮')">警告按钮</button>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer">
      <text class="footer-text">
        {{ isPC ? 'PC端已自动缩放适配' : '移动端原生显示' }}
      </text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PCDemo',
  data() {
    return {
      isPC: false,
      scaleRatio: 1.0,
      windowSize: { width: 0, height: 0 },
      demoItems: [
        {
          icon: '📱',
          title: '移动端体验',
          desc: '保持原有的移动端交互体验'
        },
        {
          icon: '💻',
          title: 'PC端适配',
          desc: '在PC端自动缩放到合适大小'
        },
        {
          icon: '🎨',
          title: '视觉一致',
          desc: '确保在不同设备上的视觉一致性'
        },
        {
          icon: '⚡',
          title: '性能优化',
          desc: '轻量级适配，不影响性能'
        }
      ]
    }
  },
  mounted() {
    this.updateDeviceInfo()
    this.setupWindowResize()
  },
  beforeDestroy() {
    // 移除窗口变化监听
    if (typeof wx !== 'undefined' && wx.offWindowResize) {
      wx.offWindowResize()
    }
  },
  methods: {
    updateDeviceInfo() {
      const app = getApp()
      if (app.globalData.pcAdapter) {
        const deviceInfo = app.globalData.pcAdapter.getDeviceInfo()
        this.isPC = deviceInfo.isPC
        this.scaleRatio = deviceInfo.scaleRatio
      }

      // 获取当前窗口大小
      const systemInfo = uni.getSystemInfoSync()
      this.windowSize = {
        width: systemInfo.windowWidth,
        height: systemInfo.windowHeight
      }
    },

    setupWindowResize() {
      // 监听PC端窗口大小变化
      if (typeof wx !== 'undefined' && wx.onWindowResize) {
        wx.onWindowResize((res) => {
          console.log('窗口大小变化:', res)
          this.windowSize = {
            width: res.size.windowWidth,
            height: res.size.windowHeight
          }

          // 显示提示
          uni.showToast({
            title: `窗口变化: ${res.size.windowWidth}x${res.size.windowHeight}`,
            icon: 'none',
            duration: 2000
          })
        })
      }
    },
    
    showMessage(type) {
      uni.showToast({
        title: '点击了' + type,
        icon: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-demo {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  .title {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
  }
  
  .subtitle {
    display: block;
    font-size: 14px;
    color: #666;
  }
}

.status-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  
  .status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    
    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
    }
  }
  
  .status-label {
    font-size: 14px;
    color: #666;
  }
  
  .status-value {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    
    &.pc-device {
      color: #52c41a;
    }
  }
}

.demo-section, .interaction-section {
  margin-bottom: 30px;
  
  .section-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16px;
  }
}

.card-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.demo-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  
  .card-icon {
    font-size: 24px;
    margin-right: 12px;
  }
  
  .card-content {
    flex: 1;
    
    .card-title {
      display: block;
      font-size: 14px;
      font-weight: bold;
      color: #333;
      margin-bottom: 4px;
    }
    
    .card-desc {
      display: block;
      font-size: 12px;
      color: #666;
      line-height: 1.4;
    }
  }
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.demo-btn {
  padding: 12px 16px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.primary {
    background: #1890ff;
    color: white;
    
    &:active {
      background: #096dd9;
    }
  }
  
  &.secondary {
    background: #f5f5f5;
    color: #666;
    
    &:active {
      background: #e6e6e6;
    }
  }
  
  &.success {
    background: #52c41a;
    color: white;
    
    &:active {
      background: #389e0d;
    }
  }
  
  &.warning {
    background: #faad14;
    color: white;
    
    &:active {
      background: #d48806;
    }
  }
}

.footer {
  text-align: center;
  margin-top: 40px;
  
  .footer-text {
    font-size: 12px;
    color: #999;
  }
}
</style>
