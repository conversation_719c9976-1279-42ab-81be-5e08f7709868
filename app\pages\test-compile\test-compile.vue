<template>
  <view class="test-compile">
    <view class="header">
      <text class="title">编译测试页面</text>
      <text class="subtitle">验证模板字符串修复</text>
    </view>
    
    <view class="test-section">
      <view class="test-item">
        <text class="label">动态class测试：</text>
        <text class="value" :class="getTestClass('success')">测试成功</text>
      </view>
      
      <view class="test-item">
        <text class="label">字符串拼接测试：</text>
        <text class="value">{{ getTestString('Hello', 'World') }}</text>
      </view>
      
      <view class="test-item">
        <text class="label">编译状态：</text>
        <text class="value success">✅ 编译成功</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TestCompile',
  methods: {
    getTestClass(type) {
      return 'test-' + type
    },
    
    getTestString(str1, str2) {
      return str1 + ' ' + str2 + '!'
    }
  }
}
</script>

<style lang="scss" scoped>
.test-compile {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  .title {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
  }
  
  .subtitle {
    display: block;
    font-size: 14px;
    color: #666;
  }
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.label {
  font-size: 14px;
  color: #666;
}

.value {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  
  &.test-success {
    color: #52c41a;
  }
  
  &.success {
    color: #52c41a;
  }
}
</style>
