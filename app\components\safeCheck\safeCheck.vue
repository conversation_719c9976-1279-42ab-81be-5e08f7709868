<template>
	<view>
		<view class="cu-modal" :class="status==true ? 'show':''">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">{{title}}</view>
					<view class="action" @tap="setStatusTap(false)">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding-xl border">
					<view class="bg-img" :style="'background-image: url('+src+');height:130rpx;'"
						@tap="refreshCodeSrcTap">
					</view>
				</view>
				<view class="padding-xl">
					<view class="cu-form-group">
						<input placeholder="请输入图形验证码" @input="inputTap" :value="code"></input>
					</view>
				</view>
				<view class="cu-bar bg-white">
					<button class="action  margin flex-sub text-green" @tap="confirmTap">
						<text :class="confirmTextClass"></text>{{confirmText}}
					</button>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	const app = getApp();
	export default {
		name: "safeCheck",
		data() {
			return {
				src: "",
				code: "",
			};
		},
		props: {
			sid: {
				type: String,
				default: ''
			},
			scene: {
				type: Number,
				default: 0
			},
			title: {
				type: String,
				default: '安全验证'
			},
			status: {
				type: Boolean,
				default: false,
			},
			confirmText: {
				type: String,
				default: ''
			},
			confirmTextClass: {
				type: String,
				default: ''
			},
		},
		watch: {
			sid(curVal, oldVal) {
				if (curVal.length == 11 && curVal != oldVal) {
					this.sid = curVal;
					this.refreshCodeSrcTap();
				}
			},
		},
		created() {
			this.code = '';
			this.refreshCodeSrcTap();
		},
		methods: {
			inputTap(options) {
				this.code = options.detail.value
			},
			async confirmTap() {
				if (this.code == "") {
					app.showToast('请输入图形验证码');
					return;
				}
				await app.globalData.service.sendSmsCode({
					code: this.code,
					scene: this.scene,
					mobile: this.sid,
				});
				this.$emit('complete', {
					action: 1
				})
				this.setStatusTap(false);
			},
			setStatusTap(status) {
				this.$emit('update:status', status)
				this.$emit('complete', {
					action: 2
				})
				this.code = '';
			},
			refreshCodeSrcTap() {
				let sid = this.sid;
				if (sid != '') {
					this.src =
						app.globalData.config.apiUrl + 'get_captcha_img?key=' + sid + '&rand=' + Math.random();
				}

			}
		}
	}
</script>
