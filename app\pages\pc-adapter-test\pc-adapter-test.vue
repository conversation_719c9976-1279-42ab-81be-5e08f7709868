<template>
  <view class="pc-adapter-test">
    <view class="container">
      <!-- 设备信息卡片 -->
      <view class="card">
        <view class="card-title">PC端适配测试</view>
        
        <view class="info-section">
          <view class="info-item">
            <text class="label">是否PC端：</text>
            <text class="value" :class="{ success: deviceInfo.isPC }">
              {{ deviceInfo.isPC ? '是' : '否' }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="label">是否微信PC端：</text>
            <text class="value" :class="{ success: deviceInfo.isPCWeixin }">
              {{ deviceInfo.isPCWeixin ? '是' : '否' }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="label">当前断点：</text>
            <text class="value breakpoint" :class="`bp-${deviceInfo.breakpoint}`">
              {{ deviceInfo.breakpoint }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="label">窗口尺寸：</text>
            <text class="value">{{ deviceInfo.windowWidth }} × {{ deviceInfo.windowHeight }}</text>
          </view>
          
          <view class="info-item">
            <text class="label">平台：</text>
            <text class="value">{{ deviceInfo.platform }}</text>
          </view>
        </view>
      </view>

      <!-- 响应式网格测试 -->
      <view class="card">
        <view class="card-title">响应式网格测试</view>
        <view class="grid-container" :class="`grid-${deviceInfo.breakpoint}`">
          <view class="grid-item" v-for="n in gridItemCount" :key="n">
            <view class="item-content">{{ n }}</view>
          </view>
        </view>
      </view>

      <!-- 响应式显示测试 -->
      <view class="card">
        <view class="card-title">响应式显示测试</view>
        
        <view class="pc-show-mobile">
          <view class="alert mobile">移动端显示</view>
        </view>
        
        <view class="pc-show-tablet">
          <view class="alert tablet">平板端显示</view>
        </view>
        
        <view class="pc-show-desktop">
          <view class="alert desktop">桌面端显示</view>
        </view>
        
        <view class="pc-show-large">
          <view class="alert large">大屏端显示</view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="card">
        <view class="button-group">
          <button class="btn primary" @click="refreshInfo">刷新信息</button>
          <button class="btn secondary" @click="changeGridItems">切换网格</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PCAdapterTest',
  data() {
    return {
      deviceInfo: {
        isPC: false,
        isPCWeixin: false,
        breakpoint: 'mobile',
        windowWidth: 0,
        windowHeight: 0,
        platform: ''
      },
      gridItemCount: 6
    }
  },
  mounted() {
    this.updateDeviceInfo()
    this.setupListener()
  },
  beforeDestroy() {
    this.removeListener()
  },
  methods: {
    updateDeviceInfo() {
      const app = getApp()
      if (app.globalData.pcAdapter) {
        this.deviceInfo = app.globalData.pcAdapter.getDeviceInfo()
      }
    },
    
    setupListener() {
      const app = getApp()
      if (app.globalData.pcAdapter) {
        this.resizeHandler = () => {
          this.updateDeviceInfo()
        }
        app.globalData.pcAdapter.onWindowResize(this.resizeHandler)
      }
    },
    
    removeListener() {
      const app = getApp()
      if (app.globalData.pcAdapter && this.resizeHandler) {
        app.globalData.pcAdapter.offWindowResize(this.resizeHandler)
      }
    },
    
    refreshInfo() {
      this.updateDeviceInfo()
      uni.showToast({
        title: '信息已刷新',
        icon: 'success'
      })
    },
    
    changeGridItems() {
      this.gridItemCount = this.gridItemCount === 6 ? 9 : 6
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-adapter-test {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 16px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
}

.info-section {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  color: #333;
  
  &.success {
    color: #52c41a;
    font-weight: bold;
  }
  
  &.breakpoint {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    
    &.bp-mobile { background: #e3f2fd; color: #1976d2; }
    &.bp-tablet { background: #f3e5f5; color: #7b1fa2; }
    &.bp-desktop { background: #e8f5e8; color: #388e3c; }
    &.bp-large { background: #fff3e0; color: #f57c00; }
  }
}

.grid-container {
  display: grid;
  gap: 16px;
  
  &.grid-mobile { grid-template-columns: 1fr; }
  &.grid-tablet { grid-template-columns: repeat(2, 1fr); }
  &.grid-desktop { grid-template-columns: repeat(3, 1fr); }
  &.grid-large { grid-template-columns: repeat(4, 1fr); }
}

.grid-item {
  .item-content {
    background: #f5f5f5;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    font-weight: bold;
    color: #666;
  }
}

.alert {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  font-weight: 500;
  text-align: center;
  
  &.mobile { background: #e3f2fd; color: #1976d2; }
  &.tablet { background: #f3e5f5; color: #7b1fa2; }
  &.desktop { background: #e8f5e8; color: #388e3c; }
  &.large { background: #fff3e0; color: #f57c00; }
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  
  &.primary {
    background: #2196f3;
    color: white;
  }
  
  &.secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
  }
}

// 响应式显示类
.pc-show-mobile { display: block; }
.pc-show-tablet { display: none; }
.pc-show-desktop { display: none; }
.pc-show-large { display: none; }

@media (min-width: 768px) {
  .pc-show-mobile { display: none; }
  .pc-show-tablet { display: block; }
}

@media (min-width: 1024px) {
  .pc-show-tablet { display: none; }
  .pc-show-desktop { display: block; }
}

@media (min-width: 1440px) {
  .pc-show-desktop { display: none; }
  .pc-show-large { display: block; }
}
</style>
