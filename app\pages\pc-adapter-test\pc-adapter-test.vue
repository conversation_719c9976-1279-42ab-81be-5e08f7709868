<template>
  <view class="pc-adapter-test">
    <view class="container">
      <!-- 设备信息卡片 -->
      <view class="card">
        <view class="card-title">PC端适配测试</view>
        
        <view class="info-section">
          <view class="info-item">
            <text class="label">是否PC端：</text>
            <text class="value" :class="{ success: deviceInfo.isPC }">
              {{ deviceInfo.isPC ? '是' : '否' }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="label">是否微信PC端：</text>
            <text class="value" :class="{ success: deviceInfo.isPCWeixin }">
              {{ deviceInfo.isPCWeixin ? '是' : '否' }}
            </text>
          </view>
          
          <view class="info-item">
            <text class="label">缩放比例：</text>
            <text class="value">{{ deviceInfo.scaleRatio || '1.0' }}</text>
          </view>
          
          <view class="info-item">
            <text class="label">窗口尺寸：</text>
            <text class="value">{{ deviceInfo.windowWidth }} × {{ deviceInfo.windowHeight }}</text>
          </view>
          
          <view class="info-item">
            <text class="label">平台：</text>
            <text class="value">{{ deviceInfo.platform }}</text>
          </view>
        </view>
      </view>

      <!-- PC端适配效果展示 -->
      <view class="card">
        <view class="card-title">PC端适配效果</view>

        <view class="mobile-only">
          <view class="alert mobile">当前在移动端显示</view>
        </view>

        <view class="pc-only">
          <view class="alert desktop">当前在PC端显示（已缩放适配）</view>
        </view>

        <view class="demo-content">
          <text class="demo-text">这是一个演示内容区域</text>
          <text class="demo-desc">在PC端会自动缩放到合适的大小，保持移动端的视觉比例</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="card">
        <view class="button-group">
          <button class="btn primary" @click="refreshInfo">刷新信息</button>
          <button class="btn secondary" @click="changeScale">调整缩放</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PCAdapterTest',
  data() {
    return {
      deviceInfo: {
        isPC: false,
        isPCWeixin: false,
        windowWidth: 0,
        windowHeight: 0,
        platform: '',
        scaleRatio: 1.0
      },
      scaleOptions: [0.6, 0.7, 0.8, 0.9, 1.0],
      currentScaleIndex: 2
    }
  },
  mounted() {
    this.updateDeviceInfo()
    this.setupListener()
  },
  beforeDestroy() {
    this.removeListener()
  },
  methods: {
    updateDeviceInfo() {
      const app = getApp()
      if (app.globalData.pcAdapter) {
        this.deviceInfo = app.globalData.pcAdapter.getDeviceInfo()
      }
    },
    
    setupListener() {
      const app = getApp()
      if (app.globalData.pcAdapter) {
        this.resizeHandler = () => {
          this.updateDeviceInfo()
        }
        app.globalData.pcAdapter.onWindowResize(this.resizeHandler)
      }
    },
    
    removeListener() {
      const app = getApp()
      if (app.globalData.pcAdapter && this.resizeHandler) {
        app.globalData.pcAdapter.offWindowResize(this.resizeHandler)
      }
    },
    
    refreshInfo() {
      this.updateDeviceInfo()
      uni.showToast({
        title: '信息已刷新',
        icon: 'success'
      })
    },
    
    changeScale() {
      if (!this.deviceInfo.isPC) {
        uni.showToast({
          title: '仅PC端支持缩放调整',
          icon: 'none'
        })
        return
      }

      this.currentScaleIndex = (this.currentScaleIndex + 1) % this.scaleOptions.length
      const newScale = this.scaleOptions[this.currentScaleIndex]

      const app = getApp()
      if (app.globalData.pcAdapter) {
        app.globalData.pcAdapter.setScaleRatio(newScale)
        this.updateDeviceInfo()
        uni.showToast({
          title: `缩放比例: ${newScale}`,
          icon: 'success'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-adapter-test {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 16px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
}

.info-section {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  color: #333;
  
  &.success {
    color: #52c41a;
    font-weight: bold;
  }
  
  &.breakpoint {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    
    &.bp-mobile { background: #e3f2fd; color: #1976d2; }
    &.bp-tablet { background: #f3e5f5; color: #7b1fa2; }
    &.bp-desktop { background: #e8f5e8; color: #388e3c; }
    &.bp-large { background: #fff3e0; color: #f57c00; }
  }
}

.demo-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;
  text-align: center;

  .demo-text {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
  }

  .demo-desc {
    display: block;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
  }
}

.alert {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  font-weight: 500;
  text-align: center;

  &.mobile {
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
  }

  &.desktop {
    background: #e8f5e8;
    color: #388e3c;
    border: 1px solid #c8e6c9;
  }
}

.button-group {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  
  &.primary {
    background: #2196f3;
    color: white;
  }
  
  &.secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
  }
}

// PC端适配显示类
.mobile-only {
  display: block;
}

.pc-only {
  display: none;
}

@media (min-width: 1024px) {
  .mobile-only {
    display: none;
  }

  .pc-only {
    display: block;
  }
}
</style>
