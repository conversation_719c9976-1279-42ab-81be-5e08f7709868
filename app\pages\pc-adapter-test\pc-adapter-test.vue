<template>
  <view class="multi-platform-test">
    <responsive-container>
      <!-- 平台信息卡片 -->
      <responsive-card title="多端适配测试" :pc-optimized="true" :hoverable="true">
        <view class="info-section">
          <view class="info-item">
            <text class="label">当前平台：</text>
            <text class="value platform-badge" :class="`platform-${adapterInfo.platform.current}`">
              {{ getPlatformName(adapterInfo.platform.current) }}
            </text>
          </view>

          <view class="info-item">
            <text class="label">是否PC端：</text>
            <text class="value" :class="{ success: adapterInfo.platform.isPC }">
              {{ adapterInfo.platform.isPC ? '是' : '否' }}
            </text>
          </view>

          <view class="info-item">
            <text class="label">屏幕类型：</text>
            <text class="value screen-badge" :class="`screen-${adapterInfo.device.screenType}`">
              {{ getScreenTypeName(adapterInfo.device.screenType) }}
            </text>
          </view>

          <view class="info-item">
            <text class="label">设备方向：</text>
            <text class="value">{{ adapterInfo.device.orientation === 'portrait' ? '竖屏' : '横屏' }}</text>
          </view>

          <view class="info-item">
            <text class="label">窗口尺寸：</text>
            <text class="value">{{ adapterInfo.windowInfo?.windowWidth }} × {{ adapterInfo.windowInfo?.windowHeight }}</text>
          </view>

          <view class="info-item" v-if="adapterInfo.platform.isPC">
            <text class="label">PC缩放比例：</text>
            <text class="value">{{ adapterInfo.config?.pcScale?.ratio || '0.8' }}</text>
          </view>
        </view>
      </responsive-card>

      <!-- 响应式布局演示 -->
      <responsive-card title="响应式布局演示" :pc-optimized="true">
        <responsive-layout type="grid" :cols="gridCols" :gap="gridGap">
          <view class="demo-item" v-for="(item, index) in demoItems" :key="index">
            <view class="item-icon">{{ item.icon }}</view>
            <text class="item-title">{{ item.title }}</text>
            <text class="item-desc">{{ item.desc }}</text>
          </view>
        </responsive-layout>
      </responsive-card>

      <!-- 平台特定显示 -->
      <responsive-card title="平台特定显示" :pc-optimized="true">
        <view class="platform-demos">
          <view class="mobile-only">
            <view class="alert mobile">移动端专用内容</view>
          </view>

          <view class="pc-only">
            <view class="alert desktop">PC端专用内容（已缩放适配）</view>
          </view>

          <view class="show-mobile">
            <view class="alert info">小屏幕显示</view>
          </view>

          <view class="show-tablet">
            <view class="alert warning">平板显示</view>
          </view>

          <view class="show-desktop">
            <view class="alert success">桌面端显示</view>
          </view>

          <view class="show-large">
            <view class="alert primary">大屏显示</view>
          </view>
        </view>
      </responsive-card>

      <!-- 交互测试 -->
      <responsive-card title="交互测试" :pc-optimized="true">
        <view class="interaction-demos">
          <view class="button-grid">
            <button class="button primary" @click="testInteraction('主要按钮')">主要按钮</button>
            <button class="button secondary" @click="testInteraction('次要按钮')">次要按钮</button>
          </view>

          <view class="touch-demo touch-feedback" @click="testTouch">
            <text>触摸反馈测试区域</text>
          </view>
        </view>
      </responsive-card>

      <!-- 操作控制 -->
      <responsive-card title="操作控制" :pc-optimized="true">
        <view class="control-section">
          <button class="button primary" @click="refreshInfo">刷新适配信息</button>
          <button class="button secondary" @click="toggleDemo">切换演示模式</button>
          <button class="button" v-if="adapterInfo.platform.isPC" @click="adjustScale">调整PC缩放</button>
        </view>
      </responsive-card>
    </responsive-container>
  </view>
</template>

<script>
import ResponsiveContainer from '@/components/responsive-container/responsive-container.vue'
import ResponsiveCard from '@/components/responsive-card/responsive-card.vue'
import ResponsiveLayout from '@/components/responsive-layout/responsive-layout.vue'

export default {
  name: 'MultiPlatformTest',
  components: {
    ResponsiveContainer,
    ResponsiveCard,
    ResponsiveLayout
  },
  data() {
    return {
      adapterInfo: {
        platform: {},
        device: {},
        config: {},
        windowInfo: null
      },

      gridCols: {
        mobile: 1,
        tablet: 2,
        desktop: 3,
        large: 4
      },

      gridGap: {
        mobile: '12px',
        tablet: '16px',
        desktop: '20px',
        large: '24px'
      },

      demoItems: [
        { icon: '📱', title: '移动端', desc: '原生移动体验' },
        { icon: '📟', title: '平板端', desc: '平板优化布局' },
        { icon: '💻', title: '桌面端', desc: 'PC端缩放适配' },
        { icon: '🖥️', title: '大屏端', desc: '大屏幕优化' },
        { icon: '🎨', title: '响应式', desc: '自适应布局' },
        { icon: '⚡', title: '性能优化', desc: '流畅交互体验' }
      ],

      scaleOptions: [0.6, 0.7, 0.8, 0.9, 1.0],
      currentScaleIndex: 2
    }
  },
  mounted() {
    this.updateAdapterInfo()
    this.setupEventListeners()
  },

  beforeDestroy() {
    this.removeEventListeners()
  },

  methods: {
    updateAdapterInfo() {
      const app = getApp()
      if (app.globalData.multiPlatformAdapter) {
        this.adapterInfo = app.globalData.multiPlatformAdapter.getAdapterInfo()
      }
    },

    setupEventListeners() {
      const app = getApp()
      if (app.globalData.multiPlatformAdapter) {
        this.resizeHandler = () => {
          this.updateAdapterInfo()
        }
        app.globalData.multiPlatformAdapter.on('resize', this.resizeHandler)
      }
    },

    removeEventListeners() {
      const app = getApp()
      if (app.globalData.multiPlatformAdapter && this.resizeHandler) {
        app.globalData.multiPlatformAdapter.off('resize', this.resizeHandler)
      }
    },

    getPlatformName(platform) {
      const names = {
        'h5': 'H5网页',
        'app': 'App应用',
        'mp-weixin': '微信小程序',
        'mp-alipay': '支付宝小程序',
        'mp-baidu': '百度小程序',
        'mp-toutiao': '字节小程序',
        'mp-qq': 'QQ小程序'
      }
      return names[platform] || platform
    },

    getScreenTypeName(screenType) {
      const names = {
        'mobile': '移动端',
        'tablet': '平板端',
        'desktop': '桌面端',
        'large': '大屏端'
      }
      return names[screenType] || screenType
    },

    refreshInfo() {
      this.updateAdapterInfo()
      uni.showToast({
        title: '适配信息已刷新',
        icon: 'success'
      })
    },

    toggleDemo() {
      // 切换演示项目数量
      const currentLength = this.demoItems.length
      if (currentLength === 6) {
        this.demoItems = this.demoItems.slice(0, 4)
      } else {
        this.demoItems = [
          { icon: '📱', title: '移动端', desc: '原生移动体验' },
          { icon: '📟', title: '平板端', desc: '平板优化布局' },
          { icon: '💻', title: '桌面端', desc: 'PC端缩放适配' },
          { icon: '🖥️', title: '大屏端', desc: '大屏幕优化' },
          { icon: '🎨', title: '响应式', desc: '自适应布局' },
          { icon: '⚡', title: '性能优化', desc: '流畅交互体验' }
        ]
      }
    },

    adjustScale() {
      if (!this.adapterInfo.platform.isPC) {
        uni.showToast({
          title: '仅PC端支持缩放调整',
          icon: 'none'
        })
        return
      }

      this.currentScaleIndex = (this.currentScaleIndex + 1) % this.scaleOptions.length
      const newScale = this.scaleOptions[this.currentScaleIndex]

      const app = getApp()
      if (app.globalData.multiPlatformAdapter) {
        app.globalData.multiPlatformAdapter.setPCScaleRatio(newScale)
        this.updateAdapterInfo()
        uni.showToast({
          title: `PC缩放比例: ${newScale}`,
          icon: 'success'
        })
      }
    },

    testInteraction(type) {
      uni.showToast({
        title: `点击了${type}`,
        icon: 'success'
      })
    },

    testTouch() {
      uni.showToast({
        title: '触摸反馈测试',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/pc-responsive.scss';

.multi-platform-test {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 16px 0;
}

.info-section {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }
}

.label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.value {
  color: #333;
  font-size: 14px;

  &.success {
    color: #52c41a;
    font-weight: bold;
  }
}

.platform-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;

  &.platform-h5 { background: #e3f2fd; color: #1976d2; }
  &.platform-app { background: #f3e5f5; color: #7b1fa2; }
  &.platform-mp-weixin { background: #e8f5e8; color: #388e3c; }
  &.platform-mp-alipay { background: #fff3e0; color: #f57c00; }
}

.screen-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;

  &.screen-mobile { background: #e3f2fd; color: #1976d2; }
  &.screen-tablet { background: #f3e5f5; color: #7b1fa2; }
  &.screen-desktop { background: #e8f5e8; color: #388e3c; }
  &.screen-large { background: #fff3e0; color: #f57c00; }
}

.demo-item {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 2px dashed #e0e0e0;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    transform: translateY(-2px);
  }

  .item-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }

  .item-title {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
  }

  .item-desc {
    display: block;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
  }
}

.platform-demos {
  .alert {
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    font-weight: 500;
    text-align: center;

    &.mobile { background: #e3f2fd; color: #1976d2; border: 1px solid #bbdefb; }
    &.desktop { background: #e8f5e8; color: #388e3c; border: 1px solid #c8e6c9; }
    &.info { background: #f0f0f0; color: #666; border: 1px solid #d9d9d9; }
    &.warning { background: #fff3e0; color: #f57c00; border: 1px solid #ffcc02; }
    &.success { background: #e8f5e8; color: #388e3c; border: 1px solid #c8e6c9; }
    &.primary { background: #f3e5f5; color: #7b1fa2; border: 1px solid #e1bee7; }
  }
}

.interaction-demos {
  .button-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 20px;
  }

  .touch-demo {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      background: #e9ecef;
      transform: scale(0.98);
    }

    text {
      color: #666;
      font-size: 14px;
    }
  }
}

.control-section {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

// 响应式优化
@include respond-to(mobile) {
  .button-grid {
    grid-template-columns: 1fr !important;
  }

  .control-section {
    flex-direction: column;
    align-items: center;
  }
}

@include respond-to(tablet) {
  .demo-item {
    .item-icon {
      font-size: 36px;
    }
  }
}

@include respond-to(desktop) {
  .demo-item {
    .item-icon {
      font-size: 40px;
    }

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }
  }

  .touch-demo:hover {
    border-color: #1890ff;
    background: #f0f8ff;
  }
}
</style>
