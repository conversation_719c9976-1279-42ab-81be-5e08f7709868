<view data-event-opts="{{[['tap',[['handleClick',['$event']]]]]}}" class="{{['responsive-card','data-v-ab6b2dd0',cardClasses]}}" style="{{(cardStyles)}}" bindtap="__e"><block wx:if="{{$slots.header||title}}"><view class="responsive-card__header data-v-ab6b2dd0"><block wx:if="{{$slots.header}}"><slot name="header"></slot></block><block wx:else><text class="responsive-card__title data-v-ab6b2dd0">{{title}}</text><block wx:if="{{subtitle}}"><text class="responsive-card__subtitle data-v-ab6b2dd0">{{subtitle}}</text></block></block></view></block><view class="responsive-card__body data-v-ab6b2dd0"><slot></slot></view><block wx:if="{{$slots.footer}}"><view class="responsive-card__footer data-v-ab6b2dd0"><slot name="footer"></slot></view></block></view>