{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/multi-platform-demo/multi-platform-demo.vue?d0d6", "webpack:///D:/桌面/thinker/app/pages/multi-platform-demo/multi-platform-demo.vue?bb1d", "webpack:///D:/桌面/thinker/app/pages/multi-platform-demo/multi-platform-demo.vue?b5c6", "webpack:///D:/桌面/thinker/app/pages/multi-platform-demo/multi-platform-demo.vue?081e", "uni-app:///pages/multi-platform-demo/multi-platform-demo.vue", "webpack:///D:/桌面/thinker/app/pages/multi-platform-demo/multi-platform-demo.vue?09c6", "webpack:///D:/桌面/thinker/app/pages/multi-platform-demo/multi-platform-demo.vue?1543"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "platformName", "deviceType", "screenType", "isPC", "pcScaleRatio", "demoItems", "icon", "title", "mounted", "methods", "updateInfo", "console", "getPlatformName", "getDeviceTypeName", "getScreenTypeName", "refreshInfo", "uni", "testToast", "adjustScale", "app"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,0BAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4I;AAC5I;AACuE;AACL;AACsC;;;AAGxG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,yFAAM;AACR,EAAE,0GAAM;AACR,EAAE,mHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuqB,CAAgB,qqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuE3rB;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC,YACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;QACA;QACA;UACA;UACA;UAEA;UACA;UACA;UACA;UAEA;YACA;UACA;QACA;MACA;QACAC;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACAC;QACAT;QACAD;MACA;IACA;IAEAW;MACAD;QACAT;QACAD;MACA;IACA;IAEAY;MACA;QACA;QACA;UACA;UACA;UAEAC;UACA;UAEAH;YACAT;YACAD;UACA;QACA;MACA;QACAK;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnLA;AAAA;AAAA;AAAA;AAAsxC,CAAgB,4sCAAG,EAAC,C;;;;;;;;;;;ACA1yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/multi-platform-demo/multi-platform-demo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/multi-platform-demo/multi-platform-demo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./multi-platform-demo.vue?vue&type=template&id=3bff8c4c&scoped=true&\"\nvar renderjs\nimport script from \"./multi-platform-demo.vue?vue&type=script&lang=js&\"\nexport * from \"./multi-platform-demo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./multi-platform-demo.vue?vue&type=style&index=0&id=3bff8c4c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3bff8c4c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/multi-platform-demo/multi-platform-demo.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./multi-platform-demo.vue?vue&type=template&id=3bff8c4c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./multi-platform-demo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./multi-platform-demo.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"multi-platform-demo\">\n    <!-- 基础信息展示 -->\n    <view class=\"info-card\">\n      <view class=\"card-title\">多端适配信息</view>\n      \n      <view class=\"info-list\">\n        <view class=\"info-item\">\n          <text class=\"label\">当前平台：</text>\n          <text class=\"value\">{{ platformName }}</text>\n        </view>\n        \n        <view class=\"info-item\">\n          <text class=\"label\">设备类型：</text>\n          <text class=\"value\">{{ deviceType }}</text>\n        </view>\n        \n        <view class=\"info-item\">\n          <text class=\"label\">屏幕类型：</text>\n          <text class=\"value\">{{ screenType }}</text>\n        </view>\n        \n        <view class=\"info-item\">\n          <text class=\"label\">是否PC端：</text>\n          <text class=\"value\" :class=\"{ 'pc-indicator': isPC }\">{{ isPC ? '是' : '否' }}</text>\n        </view>\n        \n        <view class=\"info-item\" v-if=\"isPC\">\n          <text class=\"label\">PC缩放比例：</text>\n          <text class=\"value\">{{ pcScaleRatio }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 响应式演示 -->\n    <view class=\"demo-card\">\n      <view class=\"card-title\">响应式演示</view>\n      \n      <view class=\"demo-grid\">\n        <view class=\"demo-item\" v-for=\"(item, index) in demoItems\" :key=\"index\">\n          <view class=\"item-icon\">{{ item.icon }}</view>\n          <text class=\"item-title\">{{ item.title }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 平台特定显示 -->\n    <view class=\"demo-card\">\n      <view class=\"card-title\">平台特定显示</view>\n      \n      <view class=\"platform-specific\">\n        <view class=\"mobile-only demo-alert mobile\">移动端专用</view>\n        <view class=\"pc-only demo-alert pc\">PC端专用</view>\n        <view class=\"demo-alert default\">通用内容</view>\n      </view>\n    </view>\n\n    <!-- 操作按钮 -->\n    <view class=\"demo-card\">\n      <view class=\"card-title\">操作测试</view>\n      \n      <view class=\"button-group\">\n        <button class=\"demo-btn primary\" @click=\"refreshInfo\">刷新信息</button>\n        <button class=\"demo-btn secondary\" @click=\"testToast\">测试提示</button>\n        <button class=\"demo-btn\" v-if=\"isPC\" @click=\"adjustScale\">调整缩放</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'MultiPlatformDemo',\n  data() {\n    return {\n      platformName: '未知',\n      deviceType: '未知',\n      screenType: '未知',\n      isPC: false,\n      pcScaleRatio: '0.8',\n      \n      demoItems: [\n        { icon: '📱', title: '移动端' },\n        { icon: '📟', title: '平板端' },\n        { icon: '💻', title: '桌面端' },\n        { icon: '🖥️', title: '大屏端' }\n      ]\n    }\n  },\n  \n  mounted() {\n    this.updateInfo()\n  },\n  \n  methods: {\n    updateInfo() {\n      try {\n        const app = getApp()\n        if (app.globalData && app.globalData.multiPlatformAdapter) {\n          const adapter = app.globalData.multiPlatformAdapter\n          const adapterInfo = adapter.getAdapterInfo()\n          \n          this.platformName = this.getPlatformName(adapterInfo.platform.current)\n          this.deviceType = this.getDeviceTypeName(adapterInfo.device)\n          this.screenType = this.getScreenTypeName(adapterInfo.device.screenType)\n          this.isPC = adapterInfo.platform.isPC\n          \n          if (adapterInfo.config && adapterInfo.config.pcScale) {\n            this.pcScaleRatio = adapterInfo.config.pcScale.ratio || '0.8'\n          }\n        }\n      } catch (error) {\n        console.error('获取适配信息失败:', error)\n      }\n    },\n    \n    getPlatformName(platform) {\n      const names = {\n        'h5': 'H5网页',\n        'app': 'App应用',\n        'mp-weixin': '微信小程序',\n        'mp-alipay': '支付宝小程序',\n        'mp-baidu': '百度小程序'\n      }\n      return names[platform] || platform || '未知'\n    },\n    \n    getDeviceTypeName(device) {\n      if (device.isDesktop) return '桌面设备'\n      if (device.isTablet) return '平板设备'\n      if (device.isMobile) return '移动设备'\n      return '未知设备'\n    },\n    \n    getScreenTypeName(screenType) {\n      const names = {\n        'mobile': '移动端',\n        'tablet': '平板端',\n        'desktop': '桌面端',\n        'large': '大屏端'\n      }\n      return names[screenType] || screenType || '未知'\n    },\n    \n    refreshInfo() {\n      this.updateInfo()\n      uni.showToast({\n        title: '信息已刷新',\n        icon: 'success'\n      })\n    },\n    \n    testToast() {\n      uni.showToast({\n        title: '测试成功！',\n        icon: 'success'\n      })\n    },\n    \n    adjustScale() {\n      try {\n        const app = getApp()\n        if (app.globalData && app.globalData.multiPlatformAdapter) {\n          const currentRatio = parseFloat(this.pcScaleRatio)\n          const newRatio = currentRatio >= 1.0 ? 0.6 : currentRatio + 0.1\n          \n          app.globalData.multiPlatformAdapter.setPCScaleRatio(newRatio)\n          this.pcScaleRatio = newRatio.toFixed(1)\n          \n          uni.showToast({\n            title: `缩放: ${this.pcScaleRatio}`,\n            icon: 'success'\n          })\n        }\n      } catch (error) {\n        console.error('调整缩放失败:', error)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.multi-platform-demo {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding: 16px;\n}\n\n.info-card,\n.demo-card {\n  background: #fff;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.card-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 16px;\n}\n\n.info-list {\n  .info-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 10px 0;\n    border-bottom: 1px solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n  }\n}\n\n.label {\n  font-size: 14px;\n  color: #666;\n}\n\n.value {\n  font-size: 14px;\n  color: #333;\n  \n  &.pc-indicator {\n    color: #52c41a;\n    font-weight: bold;\n  }\n}\n\n.demo-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 12px;\n}\n\n.demo-item {\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 16px;\n  text-align: center;\n  border: 2px dashed #dee2e6;\n  \n  .item-icon {\n    font-size: 24px;\n    margin-bottom: 8px;\n  }\n  \n  .item-title {\n    display: block;\n    font-size: 14px;\n    color: #666;\n  }\n}\n\n.platform-specific {\n  .demo-alert {\n    padding: 12px;\n    border-radius: 6px;\n    margin-bottom: 8px;\n    text-align: center;\n    font-weight: 500;\n    \n    &.mobile {\n      background: #e3f2fd;\n      color: #1976d2;\n    }\n    \n    &.pc {\n      background: #e8f5e8;\n      color: #388e3c;\n    }\n    \n    &.default {\n      background: #f5f5f5;\n      color: #666;\n    }\n  }\n}\n\n.button-group {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n  justify-content: center;\n}\n\n.demo-btn {\n  padding: 10px 20px;\n  border-radius: 6px;\n  border: none;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  \n  &.primary {\n    background: #1890ff;\n    color: white;\n  }\n  \n  &.secondary {\n    background: #f5f5f5;\n    color: #666;\n    border: 1px solid #d9d9d9;\n  }\n  \n  &:not(.primary):not(.secondary) {\n    background: #52c41a;\n    color: white;\n  }\n}\n\n// 响应式样式\n@media (min-width: 768px) {\n  .demo-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n}\n\n// PC/移动端显示控制\n.mobile-only {\n  display: block;\n}\n\n.pc-only {\n  display: none;\n}\n\n@media (min-width: 1024px) {\n  .mobile-only {\n    display: none;\n  }\n  \n  .pc-only {\n    display: block;\n  }\n  \n  .demo-item:hover {\n    border-color: #1890ff;\n    transform: translateY(-2px);\n    transition: all 0.3s ease;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./multi-platform-demo.vue?vue&type=style&index=0&id=3bff8c4c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./multi-platform-demo.vue?vue&type=style&index=0&id=3bff8c4c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753616751795\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}