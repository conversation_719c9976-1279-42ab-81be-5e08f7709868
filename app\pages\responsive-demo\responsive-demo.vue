<template>
  <view :class="responsiveClasses">
    <back :showBackText="false" :showBackIcon="false" :showBackLeft="false" :showHomeIcon="false"
      customClass="bg-gradual-blue text-white" title="响应式演示"></back>
    
    <!-- 调试信息 -->
    <view class="debug-info" v-if="showDebug">
      <text class="responsive-text-sm">断点: {{responsive.breakpoint}}</text>
      <text class="responsive-text-sm">布局: {{responsive.layoutMode}}</text>
      <text class="responsive-text-sm">列数: {{responsive.gridCols}}</text>
      <text class="responsive-text-sm">侧边栏: {{responsive.showSidebar ? '显示' : '隐藏'}}</text>
      <button @tap="toggleDebug" class="debug-toggle">隐藏调试</button>
    </view>
    <button v-else @tap="toggleDebug" class="debug-toggle">显示调试</button>
    
    <responsive-container>
      <!-- 侧边栏布局（大屏显示） -->
      <view class="responsive-flex" v-if="responsive.showSidebar">
        <!-- 侧边栏 -->
        <view class="responsive-sidebar responsive-padding-base">
          <view class="sidebar-section">
            <text class="responsive-text-lg text-bold text-blue">导航菜单</text>
            <view class="sidebar-menu responsive-margin-base">
              <view class="sidebar-menu-item" v-for="(item, index) in menuItems" :key="index">
                <text :class="item.icon" class="text-blue"></text>
                <text class="responsive-text-base">{{item.name}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 主内容区 -->
        <view class="responsive-main responsive-padding-base">
          <view class="desktop-content">
            <text class="responsive-text-2xl text-bold margin-bottom">桌面布局</text>
            
            <!-- 卡片网格 -->
            <responsive-grid :cols="{xs:1,sm:2,md:3,lg:4,xl:5,xxl:6}" :gap="20">
              <responsive-card 
                v-for="(item, index) in cardItems" :key="index"
                :clickable="true" 
                @tap="handleCardTap(item)">
                <view class="card-content">
                  <text :class="item.icon" class="card-icon text-blue"></text>
                  <text class="card-title responsive-text-base">{{item.title}}</text>
                  <text class="card-desc responsive-text-sm text-gray">{{item.desc}}</text>
                </view>
              </responsive-card>
            </responsive-grid>
          </view>
        </view>
      </view>
      
      <!-- 移动端布局 -->
      <view v-else class="mobile-content responsive-padding-base">
        <text class="responsive-text-2xl text-bold margin-bottom">移动端布局</text>
        
        <!-- 响应式列表 -->
        <view class="responsive-list">
          <responsive-card 
            v-for="(item, index) in cardItems" :key="index"
            class="responsive-list-item"
            :clickable="true" 
            @tap="handleCardTap(item)">
            <view class="list-item-content">
              <text :class="item.icon" class="list-icon text-blue"></text>
              <view class="list-info">
                <text class="list-title responsive-text-base">{{item.title}}</text>
                <text class="list-desc responsive-text-sm text-gray">{{item.desc}}</text>
              </view>
            </view>
          </responsive-card>
        </view>
      </view>
    </responsive-container>
  </view>
</template>

<script>
import responsiveMixin from '@/mixins/responsive.js';

export default {
  mixins: [responsiveMixin],
  data() {
    return {
      showDebug: true,
      menuItems: [
        { name: '首页', icon: 'cuIcon-home' },
        { name: '搜索', icon: 'cuIcon-search' },
        { name: '练习', icon: 'cuIcon-edit' },
        { name: '我的', icon: 'cuIcon-people' }
      ],
      cardItems: [
        { title: '功能一', desc: '这是功能一的描述', icon: 'cuIcon-apps' },
        { title: '功能二', desc: '这是功能二的描述', icon: 'cuIcon-settings' },
        { title: '功能三', desc: '这是功能三的描述', icon: 'cuIcon-message' },
        { title: '功能四', desc: '这是功能四的描述', icon: 'cuIcon-favor' },
        { title: '功能五', desc: '这是功能五的描述', icon: 'cuIcon-share' },
        { title: '功能六', desc: '这是功能六的描述', icon: 'cuIcon-info' }
      ]
    };
  },
  methods: {
    toggleDebug() {
      this.showDebug = !this.showDebug;
    },
    handleCardTap(item) {
      uni.showToast({
        title: '点击了' + item.title,
        icon: 'none'
      });
    }
  }
};
</script>

<style scoped>
.debug-info {
  position: fixed;
  top: 100rpx;
  right: 20rpx;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 20rpx;
  border-radius: 10rpx;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.debug-toggle {
  position: fixed;
  top: 100rpx;
  right: 20rpx;
  z-index: 9999;
  background: #007aff;
  color: white;
  border: none;
  padding: 10rpx 20rpx;
  border-radius: 5rpx;
  font-size: 24rpx;
}

.sidebar-menu-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  cursor: pointer;
}

.sidebar-menu-item text:first-child {
  margin-right: 15rpx;
  font-size: 32rpx;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.card-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
}

.card-title {
  margin-bottom: 10rpx;
  font-weight: bold;
}

.list-item-content {
  display: flex;
  align-items: center;
}

.list-icon {
  font-size: 50rpx;
  margin-right: 20rpx;
}

.list-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.list-title {
  margin-bottom: 8rpx;
  font-weight: bold;
}

.margin-bottom {
  margin-bottom: 30rpx;
}
</style>
