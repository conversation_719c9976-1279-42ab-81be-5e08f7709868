@charset "UTF-8";
/**
 * uni-app全局样式 - PC端适配版本
 */
/**
 * PC端适配样式
 * 实现小程序在PC端的缩放适配
 */
@media (min-width: 1024px) {
.pc-app-container.data-v-61c0b3dc {
    width: 420px;
    margin: 0 auto;
    -webkit-transform-origin: top center;
            transform-origin: top center;
    -webkit-transform: scale(0.75);
            transform: scale(0.75);
    background: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    min-height: 100vh;
}
}
@media (max-width: 1023px) {
.pc-app-container.data-v-61c0b3dc {
    width: 100%;
    margin: 0;
    -webkit-transform: none;
            transform: none;
    box-shadow: none;
    border-radius: 0;
}
}
@media (min-width: 1024px) {
.pc-page-background.data-v-61c0b3dc {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 40px 20px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}
}
@media (max-width: 1023px) {
.pc-page-background.data-v-61c0b3dc {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 0;
}
}
@media (min-width: 1024px) {
.pc-scale-container.data-v-61c0b3dc {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.pc-scale-container .pc-app-wrapper.data-v-61c0b3dc {
    width: 375px;
    height: 667px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    -webkit-transform: scale(0.75);
            transform: scale(0.75);
    -webkit-transform-origin: center;
            transform-origin: center;
    position: relative;
}
}
@media (max-width: 1023px) {
.pc-scale-container.data-v-61c0b3dc {
    width: 100%;
    height: 100vh;
}
.pc-scale-container .pc-app-wrapper.data-v-61c0b3dc {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    -webkit-transform: none;
            transform: none;
}
}
@media (max-width: 1023px) {
.pc-only.data-v-61c0b3dc {
    display: none !important;
}
}
@media (min-width: 1024px) {
.mobile-only.data-v-61c0b3dc {
    display: none !important;
}
}
@media (min-width: 1024px) {
page.data-v-61c0b3dc, page.data-v-61c0b3dc {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}
.uni-page-head.data-v-61c0b3dc {
    display: none !important;
}
.uni-page-wrapper.data-v-61c0b3dc {
    height: 100vh !important;
}
}
page.data-v-61c0b3dc {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}
@media (min-width: 1024px) {
page.data-v-61c0b3dc {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}
.uni-page-wrapper.data-v-61c0b3dc {
    width: 375px !important;
    height: 667px !important;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
    -webkit-transform-origin: center;
            transform-origin: center;
    position: relative;
}
.uni-tabbar.data-v-61c0b3dc {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
}
}
.pc-adapter-test.data-v-61c0b3dc {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 16px;
}
.container.data-v-61c0b3dc {
  max-width: 1200px;
  margin: 0 auto;
}
.card.data-v-61c0b3dc {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.card-title.data-v-61c0b3dc {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #333;
}
.info-section .info-item.data-v-61c0b3dc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}
.info-section .info-item.data-v-61c0b3dc:last-child {
  border-bottom: none;
}
.label.data-v-61c0b3dc {
  font-weight: 500;
  color: #666;
}
.value.data-v-61c0b3dc {
  color: #333;
}
.value.success.data-v-61c0b3dc {
  color: #52c41a;
  font-weight: bold;
}
.value.breakpoint.data-v-61c0b3dc {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}
.value.breakpoint.bp-mobile.data-v-61c0b3dc {
  background: #e3f2fd;
  color: #1976d2;
}
.value.breakpoint.bp-tablet.data-v-61c0b3dc {
  background: #f3e5f5;
  color: #7b1fa2;
}
.value.breakpoint.bp-desktop.data-v-61c0b3dc {
  background: #e8f5e8;
  color: #388e3c;
}
.value.breakpoint.bp-large.data-v-61c0b3dc {
  background: #fff3e0;
  color: #f57c00;
}
.demo-content.data-v-61c0b3dc {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 16px;
  text-align: center;
}
.demo-content .demo-text.data-v-61c0b3dc {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}
.demo-content .demo-desc.data-v-61c0b3dc {
  display: block;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}
.alert.data-v-61c0b3dc {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  font-weight: 500;
  text-align: center;
}
.alert.mobile.data-v-61c0b3dc {
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}
.alert.desktop.data-v-61c0b3dc {
  background: #e8f5e8;
  color: #388e3c;
  border: 1px solid #c8e6c9;
}
.button-group.data-v-61c0b3dc {
  display: flex;
  gap: 12px;
  justify-content: center;
}
.btn.data-v-61c0b3dc {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}
.btn.primary.data-v-61c0b3dc {
  background: #2196f3;
  color: white;
}
.btn.secondary.data-v-61c0b3dc {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}
.mobile-only.data-v-61c0b3dc {
  display: block;
}
.pc-only.data-v-61c0b3dc {
  display: none;
}
@media (min-width: 1024px) {
.mobile-only.data-v-61c0b3dc {
    display: none;
}
.pc-only.data-v-61c0b3dc {
    display: block;
}
}

