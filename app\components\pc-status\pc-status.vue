<template>
  <view v-if="showStatus && isPC" class="pc-status-container">
    <view class="pc-status-card">
      <view class="status-header">
        <text class="status-title">PC端状态</text>
        <text class="status-toggle" @click="toggleExpanded">
          {{ expanded ? '收起' : '展开' }}
        </text>
      </view>
      
      <view v-if="expanded" class="status-content">
        <!-- 基础信息 -->
        <view class="status-section">
          <view class="section-title">基础信息</view>
          <view class="status-item">
            <text class="label">平台：</text>
            <text class="value">{{ platformInfo.current }}</text>
          </view>
          <view class="status-item">
            <text class="label">设备类型：</text>
            <text class="value">{{ deviceInfo.screenType }}</text>
          </view>
          <view class="status-item">
            <text class="label">PC微信：</text>
            <text class="value" :class="{ 'success': platformInfo.isPCWeixin }">
              {{ platformInfo.isPCWeixin ? '是' : '否' }}
            </text>
          </view>
        </view>
        
        <!-- 窗口信息 -->
        <view class="status-section" v-if="windowState">
          <view class="section-title">窗口信息</view>
          <view class="status-item">
            <text class="label">窗口大小：</text>
            <text class="value">{{ windowState.windowWidth }}x{{ windowState.windowHeight }}</text>
          </view>
          <view class="status-item">
            <text class="label">窗口模式：</text>
            <text class="value">{{ getWindowModeText(windowState.windowMode) }}</text>
          </view>
          <view class="status-item">
            <text class="label">缩放比例：</text>
            <text class="value">{{ windowState.ratio }}</text>
          </view>
          <view class="status-item">
            <text class="label">双栏支持：</text>
            <text class="value" :class="{ 'success': windowState.supportsDualColumn }">
              {{ windowState.supportsDualColumn ? '支持' : '不支持' }}
            </text>
          </view>
        </view>
        
        <!-- 适配建议 -->
        <view class="status-section" v-if="suggestions && suggestions.suggestions.length > 0">
          <view class="section-title">适配建议</view>
          <view class="suggestions-list">
            <text 
              v-for="(suggestion, index) in suggestions.suggestions" 
              :key="index"
              class="suggestion-item"
            >
              • {{ suggestion }}
            </text>
          </view>
          <view class="status-item">
            <text class="label">推荐布局：</text>
            <text class="value">{{ suggestions.recommendedLayout }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PCStatus',
  props: {
    showStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      expanded: false,
      isPC: false,
      platformInfo: {},
      deviceInfo: {},
      windowState: null,
      suggestions: null
    }
  },
  mounted() {
    this.initStatus()
    this.setupListeners()
  },
  beforeDestroy() {
    this.removeListeners()
  },
  methods: {
    initStatus() {
      const app = getApp()
      if (app.globalData.multiPlatformAdapter) {
        const adapter = app.globalData.multiPlatformAdapter
        const adapterInfo = adapter.getAdapterInfo()
        
        this.isPC = adapter.isPC()
        this.platformInfo = adapterInfo.platform
        this.deviceInfo = adapterInfo.device
        
        if (this.isPC) {
          this.windowState = adapter.getWindowState()
          this.suggestions = adapter.getPCAdaptationSuggestions()
        }
      }
    },
    
    setupListeners() {
      // 监听窗口变化
      uni.$on('windowResize', this.handleWindowResize)
      uni.$on('applyPCStyles', this.handlePCStyles)
    },
    
    removeListeners() {
      uni.$off('windowResize', this.handleWindowResize)
      uni.$off('applyPCStyles', this.handlePCStyles)
    },
    
    handleWindowResize(data) {
      if (this.isPC) {
        const app = getApp()
        const adapter = app.globalData.multiPlatformAdapter
        this.windowState = adapter.getWindowState()
        this.suggestions = adapter.getPCAdaptationSuggestions()
      }
    },
    
    handlePCStyles(data) {
      console.log('PC状态组件 - PC样式应用:', data)
    },
    
    toggleExpanded() {
      this.expanded = !this.expanded
    },
    
    getWindowModeText(mode) {
      const modeMap = {
        'small': '小窗口',
        'normal': '正常',
        'large': '大窗口',
        'dual-column': '双栏模式'
      }
      return modeMap[mode] || mode
    }
  }
}
</script>

<style lang="scss" scoped>
.pc-status-container {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 9999;
  max-width: 300px;
}

.pc-status-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

.status-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.status-toggle {
  font-size: 12px;
  color: #007aff;
  cursor: pointer;
}

.status-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.status-section {
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 13px;
  font-weight: bold;
  color: #666;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #f0f0f0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 12px;
}

.label {
  color: #666;
  flex-shrink: 0;
}

.value {
  color: #333;
  font-weight: 500;
  text-align: right;
  
  &.success {
    color: #52c41a;
  }
}

.suggestions-list {
  margin-bottom: 8px;
}

.suggestion-item {
  display: block;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 4px;
}

/* 滚动条样式 */
.status-content::-webkit-scrollbar {
  width: 4px;
}

.status-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.status-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.status-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
