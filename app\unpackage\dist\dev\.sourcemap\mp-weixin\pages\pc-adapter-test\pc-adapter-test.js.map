{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?0d55", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?fd1a", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?42e7", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?b140", "uni-app:///pages/pc-adapter-test/pc-adapter-test.vue", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?5d3f", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?437f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "ResponsiveContainer", "ResponsiveCard", "ResponsiveLayout", "data", "adapterInfo", "platform", "device", "config", "windowInfo", "gridCols", "mobile", "tablet", "desktop", "large", "gridGap", "demoItems", "icon", "title", "desc", "scaleOptions", "currentScaleIndex", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "updateAdapterInfo", "setupEventListeners", "app", "removeEventListeners", "getPlatformName", "getScreenTypeName", "getWindowSize", "getPCScaleRatio", "refreshInfo", "uni", "toggleDemo", "adjustScale", "testInteraction", "testTouch"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmHvrB;EACAC;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEAC;QACAJ;QACAC;QACAC;QACAC;MACA;MAEAE,YACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MAEAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QACA;UACA;QACA;QACAC;MACA;IACA;IAEAC;MACA;MACA;QACAD;MACA;IACA;IAEAE;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACAC;QACAhB;QACAD;MACA;IACA;IAEAkB;MACA;MACA;MACA;QACA;MACA;QACA,kBACA;UAAAlB;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,GACA;UAAAF;UAAAC;UAAAC;QAAA,EACA;MACA;IACA;IAEAiB;MACA;QACAF;UACAhB;UACAD;QACA;QACA;MACA;MAEA;MACA;MAEA;MACA;QACAU;QACA;QACAO;UACAhB;UACAD;QACA;MACA;IACA;IAEAoB;MACAH;QACAhB;QACAD;MACA;IACA;IAEAqB;MACAJ;QACAhB;QACAD;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnSA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,wsCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pc-adapter-test/pc-adapter-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pc-adapter-test/pc-adapter-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pc-adapter-test.vue?vue&type=template&id=61c0b3dc&scoped=true&\"\nvar renderjs\nimport script from \"./pc-adapter-test.vue?vue&type=script&lang=js&\"\nexport * from \"./pc-adapter-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pc-adapter-test.vue?vue&type=style&index=0&id=61c0b3dc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"61c0b3dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pc-adapter-test/pc-adapter-test.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=template&id=61c0b3dc&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"multi-platform-test\">\n    <responsive-container>\n      <!-- 平台信息卡片 -->\n      <responsive-card title=\"多端适配测试\" :pc-optimized=\"true\" :hoverable=\"true\">\n        <view class=\"info-section\">\n          <view class=\"info-item\">\n            <text class=\"label\">当前平台：</text>\n            <text class=\"value platform-badge\" :class=\"`platform-${adapterInfo.platform.current}`\">\n              {{ getPlatformName(adapterInfo.platform.current) }}\n            </text>\n          </view>\n\n          <view class=\"info-item\">\n            <text class=\"label\">是否PC端：</text>\n            <text class=\"value\" :class=\"{ success: adapterInfo.platform.isPC }\">\n              {{ adapterInfo.platform.isPC ? '是' : '否' }}\n            </text>\n          </view>\n\n          <view class=\"info-item\">\n            <text class=\"label\">屏幕类型：</text>\n            <text class=\"value screen-badge\" :class=\"`screen-${adapterInfo.device.screenType}`\">\n              {{ getScreenTypeName(adapterInfo.device.screenType) }}\n            </text>\n          </view>\n\n          <view class=\"info-item\">\n            <text class=\"label\">设备方向：</text>\n            <text class=\"value\">{{ adapterInfo.device.orientation === 'portrait' ? '竖屏' : '横屏' }}</text>\n          </view>\n\n          <view class=\"info-item\">\n            <text class=\"label\">窗口尺寸：</text>\n            <text class=\"value\">{{ getWindowSize() }}</text>\n          </view>\n\n          <view class=\"info-item\" v-if=\"adapterInfo.platform.isPC\">\n            <text class=\"label\">PC缩放比例：</text>\n            <text class=\"value\">{{ getPCScaleRatio() }}</text>\n          </view>\n        </view>\n      </responsive-card>\n\n      <!-- 响应式布局演示 -->\n      <responsive-card title=\"响应式布局演示\" :pc-optimized=\"true\">\n        <responsive-layout type=\"grid\" :cols=\"gridCols\" :gap=\"gridGap\">\n          <view class=\"demo-item\" v-for=\"(item, index) in demoItems\" :key=\"index\">\n            <view class=\"item-icon\">{{ item.icon }}</view>\n            <text class=\"item-title\">{{ item.title }}</text>\n            <text class=\"item-desc\">{{ item.desc }}</text>\n          </view>\n        </responsive-layout>\n      </responsive-card>\n\n      <!-- 平台特定显示 -->\n      <responsive-card title=\"平台特定显示\" :pc-optimized=\"true\">\n        <view class=\"platform-demos\">\n          <view class=\"mobile-only\">\n            <view class=\"alert mobile\">移动端专用内容</view>\n          </view>\n\n          <view class=\"pc-only\">\n            <view class=\"alert desktop\">PC端专用内容（已缩放适配）</view>\n          </view>\n\n          <view class=\"show-mobile\">\n            <view class=\"alert info\">小屏幕显示</view>\n          </view>\n\n          <view class=\"show-tablet\">\n            <view class=\"alert warning\">平板显示</view>\n          </view>\n\n          <view class=\"show-desktop\">\n            <view class=\"alert success\">桌面端显示</view>\n          </view>\n\n          <view class=\"show-large\">\n            <view class=\"alert primary\">大屏显示</view>\n          </view>\n        </view>\n      </responsive-card>\n\n      <!-- 交互测试 -->\n      <responsive-card title=\"交互测试\" :pc-optimized=\"true\">\n        <view class=\"interaction-demos\">\n          <view class=\"button-grid\">\n            <button class=\"button primary\" @click=\"testInteraction('主要按钮')\">主要按钮</button>\n            <button class=\"button secondary\" @click=\"testInteraction('次要按钮')\">次要按钮</button>\n          </view>\n\n          <view class=\"touch-demo touch-feedback\" @click=\"testTouch\">\n            <text>触摸反馈测试区域</text>\n          </view>\n        </view>\n      </responsive-card>\n\n      <!-- 操作控制 -->\n      <responsive-card title=\"操作控制\" :pc-optimized=\"true\">\n        <view class=\"control-section\">\n          <button class=\"button primary\" @click=\"refreshInfo\">刷新适配信息</button>\n          <button class=\"button secondary\" @click=\"toggleDemo\">切换演示模式</button>\n          <button class=\"button\" v-if=\"adapterInfo.platform.isPC\" @click=\"adjustScale\">调整PC缩放</button>\n        </view>\n      </responsive-card>\n    </responsive-container>\n  </view>\n</template>\n\n<script>\nimport ResponsiveContainer from '@/components/responsive-container/responsive-container.vue'\nimport ResponsiveCard from '@/components/responsive-card/responsive-card.vue'\nimport ResponsiveLayout from '@/components/responsive-layout/responsive-layout.vue'\n\nexport default {\n  name: 'MultiPlatformTest',\n  components: {\n    ResponsiveContainer,\n    ResponsiveCard,\n    ResponsiveLayout\n  },\n  data() {\n    return {\n      adapterInfo: {\n        platform: {},\n        device: {},\n        config: {},\n        windowInfo: null\n      },\n\n      gridCols: {\n        mobile: 1,\n        tablet: 2,\n        desktop: 3,\n        large: 4\n      },\n\n      gridGap: {\n        mobile: '12px',\n        tablet: '16px',\n        desktop: '20px',\n        large: '24px'\n      },\n\n      demoItems: [\n        { icon: '📱', title: '移动端', desc: '原生移动体验' },\n        { icon: '📟', title: '平板端', desc: '平板优化布局' },\n        { icon: '💻', title: '桌面端', desc: 'PC端缩放适配' },\n        { icon: '🖥️', title: '大屏端', desc: '大屏幕优化' },\n        { icon: '🎨', title: '响应式', desc: '自适应布局' },\n        { icon: '⚡', title: '性能优化', desc: '流畅交互体验' }\n      ],\n\n      scaleOptions: [0.6, 0.7, 0.8, 0.9, 1.0],\n      currentScaleIndex: 2\n    }\n  },\n  mounted() {\n    this.updateAdapterInfo()\n    this.setupEventListeners()\n  },\n\n  beforeDestroy() {\n    this.removeEventListeners()\n  },\n\n  methods: {\n    updateAdapterInfo() {\n      const app = getApp()\n      if (app.globalData.multiPlatformAdapter) {\n        this.adapterInfo = app.globalData.multiPlatformAdapter.getAdapterInfo()\n      }\n    },\n\n    setupEventListeners() {\n      const app = getApp()\n      if (app.globalData.multiPlatformAdapter) {\n        this.resizeHandler = () => {\n          this.updateAdapterInfo()\n        }\n        app.globalData.multiPlatformAdapter.on('resize', this.resizeHandler)\n      }\n    },\n\n    removeEventListeners() {\n      const app = getApp()\n      if (app.globalData.multiPlatformAdapter && this.resizeHandler) {\n        app.globalData.multiPlatformAdapter.off('resize', this.resizeHandler)\n      }\n    },\n\n    getPlatformName(platform) {\n      const names = {\n        'h5': 'H5网页',\n        'app': 'App应用',\n        'mp-weixin': '微信小程序',\n        'mp-alipay': '支付宝小程序',\n        'mp-baidu': '百度小程序',\n        'mp-toutiao': '字节小程序',\n        'mp-qq': 'QQ小程序'\n      }\n      return names[platform] || platform\n    },\n\n    getScreenTypeName(screenType) {\n      const names = {\n        'mobile': '移动端',\n        'tablet': '平板端',\n        'desktop': '桌面端',\n        'large': '大屏端'\n      }\n      return names[screenType] || screenType\n    },\n\n    getWindowSize() {\n      if (this.adapterInfo.windowInfo && this.adapterInfo.windowInfo.windowWidth) {\n        return `${this.adapterInfo.windowInfo.windowWidth} × ${this.adapterInfo.windowInfo.windowHeight}`\n      }\n      return '未知'\n    },\n\n    getPCScaleRatio() {\n      if (this.adapterInfo.config && this.adapterInfo.config.pcScale && this.adapterInfo.config.pcScale.ratio) {\n        return this.adapterInfo.config.pcScale.ratio\n      }\n      return '0.8'\n    },\n\n    refreshInfo() {\n      this.updateAdapterInfo()\n      uni.showToast({\n        title: '适配信息已刷新',\n        icon: 'success'\n      })\n    },\n\n    toggleDemo() {\n      // 切换演示项目数量\n      const currentLength = this.demoItems.length\n      if (currentLength === 6) {\n        this.demoItems = this.demoItems.slice(0, 4)\n      } else {\n        this.demoItems = [\n          { icon: '📱', title: '移动端', desc: '原生移动体验' },\n          { icon: '📟', title: '平板端', desc: '平板优化布局' },\n          { icon: '💻', title: '桌面端', desc: 'PC端缩放适配' },\n          { icon: '🖥️', title: '大屏端', desc: '大屏幕优化' },\n          { icon: '🎨', title: '响应式', desc: '自适应布局' },\n          { icon: '⚡', title: '性能优化', desc: '流畅交互体验' }\n        ]\n      }\n    },\n\n    adjustScale() {\n      if (!this.adapterInfo.platform.isPC) {\n        uni.showToast({\n          title: '仅PC端支持缩放调整',\n          icon: 'none'\n        })\n        return\n      }\n\n      this.currentScaleIndex = (this.currentScaleIndex + 1) % this.scaleOptions.length\n      const newScale = this.scaleOptions[this.currentScaleIndex]\n\n      const app = getApp()\n      if (app.globalData.multiPlatformAdapter) {\n        app.globalData.multiPlatformAdapter.setPCScaleRatio(newScale)\n        this.updateAdapterInfo()\n        uni.showToast({\n          title: `PC缩放比例: ${newScale}`,\n          icon: 'success'\n        })\n      }\n    },\n\n    testInteraction(type) {\n      uni.showToast({\n        title: `点击了${type}`,\n        icon: 'success'\n      })\n    },\n\n    testTouch() {\n      uni.showToast({\n        title: '触摸反馈测试',\n        icon: 'none'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/common/css/pc-responsive.scss';\n\n.multi-platform-test {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding: 16px 0;\n}\n\n.info-section {\n  .info-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 12px 0;\n    border-bottom: 1px solid #f0f0f0;\n\n    &:last-child {\n      border-bottom: none;\n    }\n  }\n}\n\n.label {\n  font-weight: 500;\n  color: #666;\n  font-size: 14px;\n}\n\n.value {\n  color: #333;\n  font-size: 14px;\n\n  &.success {\n    color: #52c41a;\n    font-weight: bold;\n  }\n}\n\n.platform-badge {\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: bold;\n\n  &.platform-h5 { background: #e3f2fd; color: #1976d2; }\n  &.platform-app { background: #f3e5f5; color: #7b1fa2; }\n  &.platform-mp-weixin { background: #e8f5e8; color: #388e3c; }\n  &.platform-mp-alipay { background: #fff3e0; color: #f57c00; }\n}\n\n.screen-badge {\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: bold;\n\n  &.screen-mobile { background: #e3f2fd; color: #1976d2; }\n  &.screen-tablet { background: #f3e5f5; color: #7b1fa2; }\n  &.screen-desktop { background: #e8f5e8; color: #388e3c; }\n  &.screen-large { background: #fff3e0; color: #f57c00; }\n}\n\n.demo-item {\n  background: #fff;\n  border-radius: 8px;\n  padding: 20px;\n  text-align: center;\n  border: 2px dashed #e0e0e0;\n  transition: all 0.3s ease;\n\n  &:hover {\n    border-color: #1890ff;\n    transform: translateY(-2px);\n  }\n\n  .item-icon {\n    font-size: 32px;\n    margin-bottom: 12px;\n  }\n\n  .item-title {\n    display: block;\n    font-size: 16px;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 8px;\n  }\n\n  .item-desc {\n    display: block;\n    font-size: 12px;\n    color: #666;\n    line-height: 1.4;\n  }\n}\n\n.platform-demos {\n  .alert {\n    padding: 12px;\n    border-radius: 6px;\n    margin-bottom: 8px;\n    font-weight: 500;\n    text-align: center;\n\n    &.mobile { background: #e3f2fd; color: #1976d2; border: 1px solid #bbdefb; }\n    &.desktop { background: #e8f5e8; color: #388e3c; border: 1px solid #c8e6c9; }\n    &.info { background: #f0f0f0; color: #666; border: 1px solid #d9d9d9; }\n    &.warning { background: #fff3e0; color: #f57c00; border: 1px solid #ffcc02; }\n    &.success { background: #e8f5e8; color: #388e3c; border: 1px solid #c8e6c9; }\n    &.primary { background: #f3e5f5; color: #7b1fa2; border: 1px solid #e1bee7; }\n  }\n}\n\n.interaction-demos {\n  .button-grid {\n    display: grid;\n    grid-template-columns: repeat(2, 1fr);\n    gap: 12px;\n    margin-bottom: 20px;\n  }\n\n  .touch-demo {\n    background: #f8f9fa;\n    border: 2px dashed #dee2e6;\n    border-radius: 8px;\n    padding: 40px 20px;\n    text-align: center;\n    cursor: pointer;\n    transition: all 0.2s ease;\n\n    &:active {\n      background: #e9ecef;\n      transform: scale(0.98);\n    }\n\n    text {\n      color: #666;\n      font-size: 14px;\n    }\n  }\n}\n\n.control-section {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n  justify-content: center;\n}\n\n// 响应式优化\n@include respond-to(mobile) {\n  .button-grid {\n    grid-template-columns: 1fr !important;\n  }\n\n  .control-section {\n    flex-direction: column;\n    align-items: center;\n  }\n}\n\n@include respond-to(tablet) {\n  .demo-item {\n    .item-icon {\n      font-size: 36px;\n    }\n  }\n}\n\n@include respond-to(desktop) {\n  .demo-item {\n    .item-icon {\n      font-size: 40px;\n    }\n\n    &:hover {\n      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);\n    }\n  }\n\n  .touch-demo:hover {\n    border-color: #1890ff;\n    background: #f0f8ff;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=style&index=0&id=61c0b3dc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=style&index=0&id=61c0b3dc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753616751921\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}