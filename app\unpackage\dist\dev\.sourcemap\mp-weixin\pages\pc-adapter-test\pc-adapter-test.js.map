{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?fd1a", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?42e7", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?b140", "uni-app:///pages/pc-adapter-test/pc-adapter-test.vue", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?5d3f", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?437f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "deviceInfo", "isPC", "isPCWeixin", "windowWidth", "windowHeight", "platform", "scaleRatio", "scaleOptions", "currentScaleIndex", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "updateDeviceInfo", "setupListener", "app", "removeListener", "refreshInfo", "uni", "title", "icon", "changeScale"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqEvrB;EACAC;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QACA;UACA;QACA;QACAC;MACA;IACA;IAEAC;MACA;MACA;QACAD;MACA;IACA;IAEAE;MACA;MACAC;QACAC;QACAC;MACA;IACA;IAEAC;MACA;QACAH;UACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;MAEA;MACA;QACAL;QACA;QACAG;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpJA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,wsCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pc-adapter-test/pc-adapter-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pc-adapter-test/pc-adapter-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pc-adapter-test.vue?vue&type=template&id=61c0b3dc&scoped=true&\"\nvar renderjs\nimport script from \"./pc-adapter-test.vue?vue&type=script&lang=js&\"\nexport * from \"./pc-adapter-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pc-adapter-test.vue?vue&type=style&index=0&id=61c0b3dc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"61c0b3dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pc-adapter-test/pc-adapter-test.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=template&id=61c0b3dc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"pc-adapter-test\">\n    <view class=\"container\">\n      <!-- 设备信息卡片 -->\n      <view class=\"card\">\n        <view class=\"card-title\">PC端适配测试</view>\n        \n        <view class=\"info-section\">\n          <view class=\"info-item\">\n            <text class=\"label\">是否PC端：</text>\n            <text class=\"value\" :class=\"{ success: deviceInfo.isPC }\">\n              {{ deviceInfo.isPC ? '是' : '否' }}\n            </text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"label\">是否微信PC端：</text>\n            <text class=\"value\" :class=\"{ success: deviceInfo.isPCWeixin }\">\n              {{ deviceInfo.isPCWeixin ? '是' : '否' }}\n            </text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"label\">缩放比例：</text>\n            <text class=\"value\">{{ deviceInfo.scaleRatio || '1.0' }}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"label\">窗口尺寸：</text>\n            <text class=\"value\">{{ deviceInfo.windowWidth }} × {{ deviceInfo.windowHeight }}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"label\">平台：</text>\n            <text class=\"value\">{{ deviceInfo.platform }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- PC端适配效果展示 -->\n      <view class=\"card\">\n        <view class=\"card-title\">PC端适配效果</view>\n\n        <view class=\"mobile-only\">\n          <view class=\"alert mobile\">当前在移动端显示</view>\n        </view>\n\n        <view class=\"pc-only\">\n          <view class=\"alert desktop\">当前在PC端显示（已缩放适配）</view>\n        </view>\n\n        <view class=\"demo-content\">\n          <text class=\"demo-text\">这是一个演示内容区域</text>\n          <text class=\"demo-desc\">在PC端会自动缩放到合适的大小，保持移动端的视觉比例</text>\n        </view>\n      </view>\n\n      <!-- 操作按钮 -->\n      <view class=\"card\">\n        <view class=\"button-group\">\n          <button class=\"btn primary\" @click=\"refreshInfo\">刷新信息</button>\n          <button class=\"btn secondary\" @click=\"changeScale\">调整缩放</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'PCAdapterTest',\n  data() {\n    return {\n      deviceInfo: {\n        isPC: false,\n        isPCWeixin: false,\n        windowWidth: 0,\n        windowHeight: 0,\n        platform: '',\n        scaleRatio: 1.0\n      },\n      scaleOptions: [0.6, 0.7, 0.8, 0.9, 1.0],\n      currentScaleIndex: 2\n    }\n  },\n  mounted() {\n    this.updateDeviceInfo()\n    this.setupListener()\n  },\n  beforeDestroy() {\n    this.removeListener()\n  },\n  methods: {\n    updateDeviceInfo() {\n      const app = getApp()\n      if (app.globalData.pcAdapter) {\n        this.deviceInfo = app.globalData.pcAdapter.getDeviceInfo()\n      }\n    },\n    \n    setupListener() {\n      const app = getApp()\n      if (app.globalData.pcAdapter) {\n        this.resizeHandler = () => {\n          this.updateDeviceInfo()\n        }\n        app.globalData.pcAdapter.onWindowResize(this.resizeHandler)\n      }\n    },\n    \n    removeListener() {\n      const app = getApp()\n      if (app.globalData.pcAdapter && this.resizeHandler) {\n        app.globalData.pcAdapter.offWindowResize(this.resizeHandler)\n      }\n    },\n    \n    refreshInfo() {\n      this.updateDeviceInfo()\n      uni.showToast({\n        title: '信息已刷新',\n        icon: 'success'\n      })\n    },\n    \n    changeScale() {\n      if (!this.deviceInfo.isPC) {\n        uni.showToast({\n          title: '仅PC端支持缩放调整',\n          icon: 'none'\n        })\n        return\n      }\n\n      this.currentScaleIndex = (this.currentScaleIndex + 1) % this.scaleOptions.length\n      const newScale = this.scaleOptions[this.currentScaleIndex]\n\n      const app = getApp()\n      if (app.globalData.pcAdapter) {\n        app.globalData.pcAdapter.setScaleRatio(newScale)\n        this.updateDeviceInfo()\n        uni.showToast({\n          title: `缩放比例: ${newScale}`,\n          icon: 'success'\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pc-adapter-test {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding: 16px;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.card-title {\n  font-size: 18px;\n  font-weight: bold;\n  margin-bottom: 16px;\n  color: #333;\n}\n\n.info-section {\n  .info-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 12px 0;\n    border-bottom: 1px solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n  }\n}\n\n.label {\n  font-weight: 500;\n  color: #666;\n}\n\n.value {\n  color: #333;\n  \n  &.success {\n    color: #52c41a;\n    font-weight: bold;\n  }\n  \n  &.breakpoint {\n    padding: 4px 8px;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: bold;\n    \n    &.bp-mobile { background: #e3f2fd; color: #1976d2; }\n    &.bp-tablet { background: #f3e5f5; color: #7b1fa2; }\n    &.bp-desktop { background: #e8f5e8; color: #388e3c; }\n    &.bp-large { background: #fff3e0; color: #f57c00; }\n  }\n}\n\n.demo-content {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n  text-align: center;\n\n  .demo-text {\n    display: block;\n    font-size: 16px;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 8px;\n  }\n\n  .demo-desc {\n    display: block;\n    font-size: 14px;\n    color: #666;\n    line-height: 1.5;\n  }\n}\n\n.alert {\n  padding: 12px;\n  border-radius: 6px;\n  margin-bottom: 8px;\n  font-weight: 500;\n  text-align: center;\n\n  &.mobile {\n    background: #e3f2fd;\n    color: #1976d2;\n    border: 1px solid #bbdefb;\n  }\n\n  &.desktop {\n    background: #e8f5e8;\n    color: #388e3c;\n    border: 1px solid #c8e6c9;\n  }\n}\n\n.button-group {\n  display: flex;\n  gap: 12px;\n  justify-content: center;\n}\n\n.btn {\n  padding: 10px 20px;\n  border-radius: 6px;\n  border: none;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  \n  &.primary {\n    background: #2196f3;\n    color: white;\n  }\n  \n  &.secondary {\n    background: #f5f5f5;\n    color: #666;\n    border: 1px solid #ddd;\n  }\n}\n\n// PC端适配显示类\n.mobile-only {\n  display: block;\n}\n\n.pc-only {\n  display: none;\n}\n\n@media (min-width: 1024px) {\n  .mobile-only {\n    display: none;\n  }\n\n  .pc-only {\n    display: block;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=style&index=0&id=61c0b3dc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=style&index=0&id=61c0b3dc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753613200545\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}