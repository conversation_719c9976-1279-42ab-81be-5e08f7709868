{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?fd1a", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?42e7", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?b140", "uni-app:///pages/pc-adapter-test/pc-adapter-test.vue", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?5d3f", "webpack:///D:/桌面/thinker/app/pages/pc-adapter-test/pc-adapter-test.vue?437f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "deviceInfo", "isPC", "isPCWeixin", "breakpoint", "windowWidth", "windowHeight", "platform", "gridItemCount", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "updateDeviceInfo", "setupListener", "app", "removeListener", "refreshInfo", "uni", "title", "icon", "changeGridItems"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,iqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoFvrB;EACAC;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;QACA;UACA;QACA;QACAC;MACA;IACA;IAEAC;MACA;MACA;QACAD;MACA;IACA;IAEAE;MACA;MACAC;QACAC;QACAC;MACA;IACA;IAEAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/IA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,wsCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pc-adapter-test/pc-adapter-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pc-adapter-test/pc-adapter-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pc-adapter-test.vue?vue&type=template&id=61c0b3dc&scoped=true&\"\nvar renderjs\nimport script from \"./pc-adapter-test.vue?vue&type=script&lang=js&\"\nexport * from \"./pc-adapter-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pc-adapter-test.vue?vue&type=style&index=0&id=61c0b3dc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"61c0b3dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pc-adapter-test/pc-adapter-test.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=template&id=61c0b3dc&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"pc-adapter-test\">\n    <view class=\"container\">\n      <!-- 设备信息卡片 -->\n      <view class=\"card\">\n        <view class=\"card-title\">PC端适配测试</view>\n        \n        <view class=\"info-section\">\n          <view class=\"info-item\">\n            <text class=\"label\">是否PC端：</text>\n            <text class=\"value\" :class=\"{ success: deviceInfo.isPC }\">\n              {{ deviceInfo.isPC ? '是' : '否' }}\n            </text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"label\">是否微信PC端：</text>\n            <text class=\"value\" :class=\"{ success: deviceInfo.isPCWeixin }\">\n              {{ deviceInfo.isPCWeixin ? '是' : '否' }}\n            </text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"label\">当前断点：</text>\n            <text class=\"value breakpoint\" :class=\"`bp-${deviceInfo.breakpoint}`\">\n              {{ deviceInfo.breakpoint }}\n            </text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"label\">窗口尺寸：</text>\n            <text class=\"value\">{{ deviceInfo.windowWidth }} × {{ deviceInfo.windowHeight }}</text>\n          </view>\n          \n          <view class=\"info-item\">\n            <text class=\"label\">平台：</text>\n            <text class=\"value\">{{ deviceInfo.platform }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 响应式网格测试 -->\n      <view class=\"card\">\n        <view class=\"card-title\">响应式网格测试</view>\n        <view class=\"grid-container\" :class=\"`grid-${deviceInfo.breakpoint}`\">\n          <view class=\"grid-item\" v-for=\"n in gridItemCount\" :key=\"n\">\n            <view class=\"item-content\">{{ n }}</view>\n          </view>\n        </view>\n      </view>\n\n      <!-- 响应式显示测试 -->\n      <view class=\"card\">\n        <view class=\"card-title\">响应式显示测试</view>\n        \n        <view class=\"pc-show-mobile\">\n          <view class=\"alert mobile\">移动端显示</view>\n        </view>\n        \n        <view class=\"pc-show-tablet\">\n          <view class=\"alert tablet\">平板端显示</view>\n        </view>\n        \n        <view class=\"pc-show-desktop\">\n          <view class=\"alert desktop\">桌面端显示</view>\n        </view>\n        \n        <view class=\"pc-show-large\">\n          <view class=\"alert large\">大屏端显示</view>\n        </view>\n      </view>\n\n      <!-- 操作按钮 -->\n      <view class=\"card\">\n        <view class=\"button-group\">\n          <button class=\"btn primary\" @click=\"refreshInfo\">刷新信息</button>\n          <button class=\"btn secondary\" @click=\"changeGridItems\">切换网格</button>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'PCAdapterTest',\n  data() {\n    return {\n      deviceInfo: {\n        isPC: false,\n        isPCWeixin: false,\n        breakpoint: 'mobile',\n        windowWidth: 0,\n        windowHeight: 0,\n        platform: ''\n      },\n      gridItemCount: 6\n    }\n  },\n  mounted() {\n    this.updateDeviceInfo()\n    this.setupListener()\n  },\n  beforeDestroy() {\n    this.removeListener()\n  },\n  methods: {\n    updateDeviceInfo() {\n      const app = getApp()\n      if (app.globalData.pcAdapter) {\n        this.deviceInfo = app.globalData.pcAdapter.getDeviceInfo()\n      }\n    },\n    \n    setupListener() {\n      const app = getApp()\n      if (app.globalData.pcAdapter) {\n        this.resizeHandler = () => {\n          this.updateDeviceInfo()\n        }\n        app.globalData.pcAdapter.onWindowResize(this.resizeHandler)\n      }\n    },\n    \n    removeListener() {\n      const app = getApp()\n      if (app.globalData.pcAdapter && this.resizeHandler) {\n        app.globalData.pcAdapter.offWindowResize(this.resizeHandler)\n      }\n    },\n    \n    refreshInfo() {\n      this.updateDeviceInfo()\n      uni.showToast({\n        title: '信息已刷新',\n        icon: 'success'\n      })\n    },\n    \n    changeGridItems() {\n      this.gridItemCount = this.gridItemCount === 6 ? 9 : 6\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pc-adapter-test {\n  min-height: 100vh;\n  background: #f5f5f5;\n  padding: 16px;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  margin-bottom: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.card-title {\n  font-size: 18px;\n  font-weight: bold;\n  margin-bottom: 16px;\n  color: #333;\n}\n\n.info-section {\n  .info-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 12px 0;\n    border-bottom: 1px solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n  }\n}\n\n.label {\n  font-weight: 500;\n  color: #666;\n}\n\n.value {\n  color: #333;\n  \n  &.success {\n    color: #52c41a;\n    font-weight: bold;\n  }\n  \n  &.breakpoint {\n    padding: 4px 8px;\n    border-radius: 4px;\n    font-size: 12px;\n    font-weight: bold;\n    \n    &.bp-mobile { background: #e3f2fd; color: #1976d2; }\n    &.bp-tablet { background: #f3e5f5; color: #7b1fa2; }\n    &.bp-desktop { background: #e8f5e8; color: #388e3c; }\n    &.bp-large { background: #fff3e0; color: #f57c00; }\n  }\n}\n\n.grid-container {\n  display: grid;\n  gap: 16px;\n  \n  &.grid-mobile { grid-template-columns: 1fr; }\n  &.grid-tablet { grid-template-columns: repeat(2, 1fr); }\n  &.grid-desktop { grid-template-columns: repeat(3, 1fr); }\n  &.grid-large { grid-template-columns: repeat(4, 1fr); }\n}\n\n.grid-item {\n  .item-content {\n    background: #f5f5f5;\n    border: 2px dashed #ddd;\n    border-radius: 8px;\n    padding: 20px;\n    text-align: center;\n    font-weight: bold;\n    color: #666;\n  }\n}\n\n.alert {\n  padding: 12px;\n  border-radius: 6px;\n  margin-bottom: 8px;\n  font-weight: 500;\n  text-align: center;\n  \n  &.mobile { background: #e3f2fd; color: #1976d2; }\n  &.tablet { background: #f3e5f5; color: #7b1fa2; }\n  &.desktop { background: #e8f5e8; color: #388e3c; }\n  &.large { background: #fff3e0; color: #f57c00; }\n}\n\n.button-group {\n  display: flex;\n  gap: 12px;\n  justify-content: center;\n}\n\n.btn {\n  padding: 10px 20px;\n  border-radius: 6px;\n  border: none;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  \n  &.primary {\n    background: #2196f3;\n    color: white;\n  }\n  \n  &.secondary {\n    background: #f5f5f5;\n    color: #666;\n    border: 1px solid #ddd;\n  }\n}\n\n// 响应式显示类\n.pc-show-mobile { display: block; }\n.pc-show-tablet { display: none; }\n.pc-show-desktop { display: none; }\n.pc-show-large { display: none; }\n\n@media (min-width: 768px) {\n  .pc-show-mobile { display: none; }\n  .pc-show-tablet { display: block; }\n}\n\n@media (min-width: 1024px) {\n  .pc-show-tablet { display: none; }\n  .pc-show-desktop { display: block; }\n}\n\n@media (min-width: 1440px) {\n  .pc-show-desktop { display: none; }\n  .pc-show-large { display: block; }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=style&index=0&id=61c0b3dc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pc-adapter-test.vue?vue&type=style&index=0&id=61c0b3dc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753612597847\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}