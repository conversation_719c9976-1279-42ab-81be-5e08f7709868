/**
 * 多端适配核心模块
 * 统一的设备检测、平台判断和适配控制系统
 */

class MultiPlatformAdapter {
    constructor() {
        // 设备信息
        this.deviceInfo = null;
        this.windowInfo = null;

        // 平台检测
        this.platform = {
            isApp: false,           // App端
            isH5: false,            // H5端
            isMPWeixin: false,      // 微信小程序
            isMPAlipay: false,      // 支付宝小程序
            isMPBaidu: false,       // 百度小程序
            isMPToutiao: false,     // 字节跳动小程序
            isMPQQ: false,          // QQ小程序
            isPC: false,            // PC端
            isPCWeixin: false,      // 微信PC端
            current: 'unknown'      // 当前平台
        };

        // 设备类型
        this.device = {
            isMobile: true,
            isTablet: false,
            isDesktop: false,
            screenType: 'mobile',   // mobile, tablet, desktop, large
            orientation: 'portrait' // portrait, landscape
        };

        // 适配配置
        this.config = {
            // PC端缩放配置
            pcScale: {
                enabled: true,
                ratio: 0.8,
                minRatio: 0.6,
                maxRatio: 1.2,
                containerWidth: 375,
                containerHeight: 667
            },

            // 响应式断点
            breakpoints: {
                mobile: { min: 0, max: 767 },
                tablet: { min: 768, max: 1023 },
                desktop: { min: 1024, max: 1439 },
                large: { min: 1440, max: Infinity }
            },

            // 平台特定配置
            platformConfig: {
                h5: { enablePCScale: true },
                mpWeixin: { enablePCScale: true },
                app: { enablePCScale: false }
            }
        };

        // 回调函数
        this.callbacks = {
            resize: [],
            orientationChange: [],
            platformChange: []
        };

        this.init();
    }

    /**
     * 初始化多端适配器
     */
    init() {
        this.detectPlatform();
        this.detectDevice();
        this.detectScreenType();
        this.setupEventListeners();
        this.applyAdaptation();

        console.log('MultiPlatformAdapter initialized:', {
            platform: this.platform,
            device: this.device,
            config: this.config
        });
    }

    /**
     * 检测运行平台
     */
    detectPlatform() {
        // #ifdef APP-PLUS
        this.platform.isApp = true;
        this.platform.current = 'app';
        // #endif

        // #ifdef H5
        this.platform.isH5 = true;
        this.platform.current = 'h5';
        // #endif

        // #ifdef MP-WEIXIN
        this.platform.isMPWeixin = true;
        this.platform.current = 'mp-weixin';
        // #endif

        // #ifdef MP-ALIPAY
        this.platform.isMPAlipay = true;
        this.platform.current = 'mp-alipay';
        // #endif

        // #ifdef MP-BAIDU
        this.platform.isMPBaidu = true;
        this.platform.current = 'mp-baidu';
        // #endif

        // #ifdef MP-TOUTIAO
        this.platform.isMPToutiao = true;
        this.platform.current = 'mp-toutiao';
        // #endif

        // #ifdef MP-QQ
        this.platform.isMPQQ = true;
        this.platform.current = 'mp-qq';
        // #endif

        // PC端检测
        this.detectPCEnvironment();
    }

    /**
     * 检测PC环境
     */
    detectPCEnvironment() {
        try {
            this.deviceInfo = uni.getDeviceInfo();
            const platform = this.deviceInfo.platform;
            const userAgent = this.deviceInfo.userAgent || '';

            // PC端判断逻辑
            this.platform.isPC = platform === 'windows' ||
                                 platform === 'mac' ||
                                 platform === 'linux' ||
                                 /Windows|Mac|Linux/.test(userAgent);

            // 微信PC端检测
            if (this.platform.isMPWeixin && this.platform.isPC) {
                this.platform.isPCWeixin = /MicroMessenger/.test(userAgent);
            }

            // H5 PC端检测
            if (this.platform.isH5 && this.platform.isPC) {
                // 通过屏幕尺寸和用户代理进一步确认
                const windowInfo = uni.getWindowInfo();
                if (windowInfo.windowWidth >= 1024) {
                    this.platform.isPC = true;
                }
            }

        } catch (error) {
            console.error('MultiPlatformAdapter - PC detection failed:', error);
        }
    }

    /**
     * 检测设备信息
     */
    detectDevice() {
        try {
            this.deviceInfo = uni.getDeviceInfo();
            this.windowInfo = uni.getWindowInfo();

            // 设备类型判断
            const { platform, osName } = this.deviceInfo;

            this.device.isMobile = platform === 'android' || platform === 'ios' ||
                                  osName === 'android' || osName === 'ios';

            this.device.isTablet = this.detectTablet();
            this.device.isDesktop = this.platform.isPC;

            // 屏幕方向检测
            this.detectOrientation();

        } catch (error) {
            console.error('MultiPlatformAdapter - Device detection failed:', error);
        }
    }

    /**
     * 检测平板设备
     */
    detectTablet() {
        if (!this.windowInfo) return false;

        const { windowWidth, windowHeight } = this.windowInfo;
        const minSize = Math.min(windowWidth, windowHeight);
        const maxSize = Math.max(windowWidth, windowHeight);

        // 平板判断逻辑：屏幕尺寸在768-1023之间
        return minSize >= 768 && maxSize <= 1366 && !this.platform.isPC;
    }

    /**
     * 检测屏幕方向
     */
    detectOrientation() {
        if (!this.windowInfo) return;

        const { windowWidth, windowHeight } = this.windowInfo;
        this.device.orientation = windowWidth > windowHeight ? 'landscape' : 'portrait';
    }

    /**
     * 检测屏幕类型
     */
    detectScreenType() {
        if (!this.windowInfo) return;

        const width = this.windowInfo.windowWidth;

        for (const [type, range] of Object.entries(this.config.breakpoints)) {
            if (width >= range.min && width <= range.max) {
                this.device.screenType = type;
                break;
            }
        }

        // 更新设备类型标识
        this.device.isMobile = this.device.screenType === 'mobile';
        this.device.isTablet = this.device.screenType === 'tablet';
        this.device.isDesktop = this.device.screenType === 'desktop' || this.device.screenType === 'large';
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 窗口大小变化监听
        this.setupResizeListener();

        // 屏幕方向变化监听
        this.setupOrientationListener();
    }

    /**
     * 设置窗口大小变化监听
     */
    setupResizeListener() {
        // 微信小程序PC端
        // #ifdef MP-WEIXIN
        if (this.platform.isPCWeixin) {
            try {
                wx.onWindowResize((res) => {
                    this.handleResize(res.size);
                });
            } catch (error) {
                console.error('MultiPlatformAdapter - WeChat resize listener failed:', error);
            }
        }
        // #endif

        // H5端
        // #ifdef H5
        if (typeof window !== 'undefined') {
            const resizeHandler = () => {
                const windowInfo = uni.getWindowInfo();
                this.handleResize(windowInfo);
            };

            window.addEventListener('resize', resizeHandler);
            window.addEventListener('orientationchange', resizeHandler);
        }
        // #endif

        // App端
        // #ifdef APP-PLUS
        // App端通常不需要监听窗口变化，但可以监听屏幕方向变化
        // #endif
    }

    /**
     * 设置屏幕方向变化监听
     */
    setupOrientationListener() {
        // #ifdef H5
        if (typeof window !== 'undefined' && 'orientation' in window) {
            window.addEventListener('orientationchange', () => {
                setTimeout(() => {
                    this.detectOrientation();
                    this.triggerCallbacks('orientationChange', {
                        orientation: this.device.orientation
                    });
                }, 100);
            });
        }
        // #endif
    }

    /**
     * 处理窗口大小变化
     */
    handleResize(newWindowInfo) {
        const oldScreenType = this.device.screenType;
        const oldOrientation = this.device.orientation;

        // 更新窗口信息
        this.windowInfo = { ...this.windowInfo, ...newWindowInfo };

        // 重新检测屏幕类型和方向
        this.detectScreenType();
        this.detectOrientation();

        // 如果屏幕类型发生变化，重新应用适配
        if (oldScreenType !== this.device.screenType) {
            this.applyAdaptation();
        }

        // 触发回调
        this.triggerCallbacks('resize', {
            windowInfo: this.windowInfo,
            device: this.device,
            platform: this.platform,
            oldScreenType,
            oldOrientation
        });
    }

    /**
     * 应用多端适配
     */
    applyAdaptation() {
        // 应用PC端缩放适配
        if (this.shouldApplyPCScale()) {
            this.applyPCScale();
        }

        // 应用平台特定样式
        this.applyPlatformStyles();

        // 应用响应式样式
        this.applyResponsiveStyles();
    }

    /**
     * 判断是否应该应用PC端缩放
     */
    shouldApplyPCScale() {
        if (!this.config.pcScale.enabled) return false;
        if (!this.platform.isPC) return false;

        const platformConfig = this.config.platformConfig[this.platform.current];
        return platformConfig && platformConfig.enablePCScale;
    }

    /**
     * 应用PC端缩放适配
     */
    applyPCScale() {
        try {
            // #ifdef H5
            if (typeof document !== 'undefined') {
                this.applyH5PCScale();
            }
            // #endif

            // #ifdef MP-WEIXIN
            if (this.platform.isPCWeixin) {
                this.applyWeixinPCScale();
            }
            // #endif

            console.log('MultiPlatformAdapter - PC scale applied');
        } catch (error) {
            console.error('MultiPlatformAdapter - PC scale failed:', error);
        }
    }

    /**
     * H5端PC缩放实现
     */
    applyH5PCScale() {
        if (!document.body.classList.contains('multi-platform-pc')) {
            document.body.classList.add('multi-platform-pc');

            const style = document.createElement('style');
            style.id = 'multi-platform-pc-style';
            style.textContent = this.generatePCScaleCSS();
            document.head.appendChild(style);
        }
    }

    /**
     * 微信小程序PC端缩放实现
     */
    applyWeixinPCScale() {
        // 微信小程序PC端通过CSS实现缩放
        // 在全局样式中已经定义了相关样式
        console.log('WeChat PC scale applied via global CSS');
    }

    /**
     * 生成PC缩放CSS
     */
    generatePCScaleCSS() {
        const { ratio, containerWidth, containerHeight } = this.config.pcScale;

        return `
            .multi-platform-pc {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                margin: 0;
                padding: 20px;
                transition: all 0.3s ease;
            }

            .multi-platform-pc .uni-app,
            .multi-platform-pc #app {
                width: ${containerWidth}px !important;
                height: ${containerHeight}px !important;
                max-width: ${containerWidth}px !important;
                max-height: ${containerHeight}px !important;
                background: #fff;
                border-radius: 12px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                overflow: hidden;
                transform: scale(${ratio});
                transform-origin: center;
                position: relative;
                transition: transform 0.3s ease;
            }

            .multi-platform-pc .uni-page-wrapper {
                border-radius: 12px;
                overflow: hidden;
            }

            @media (max-width: 1023px) {
                .multi-platform-pc {
                    background: #f5f5f5;
                    padding: 0;
                }

                .multi-platform-pc .uni-app,
                .multi-platform-pc #app {
                    width: 100% !important;
                    height: 100vh !important;
                    max-width: none !important;
                    max-height: none !important;
                    border-radius: 0;
                    box-shadow: none;
                    transform: none;
                }
            }
        `;
    }

    /**
     * 应用平台特定样式
     */
    applyPlatformStyles() {
        try {
            // #ifdef H5
            if (typeof document !== 'undefined') {
                document.body.classList.add('platform-h5');
                if (this.platform.isPC) {
                    document.body.classList.add('platform-h5-pc');
                }
            }
            // #endif

            // 其他平台的样式应用可以在这里扩展
        } catch (error) {
            console.error('MultiPlatformAdapter - Platform styles failed:', error);
        }
    }

    /**
     * 应用响应式样式
     */
    applyResponsiveStyles() {
        try {
            // #ifdef H5
            if (typeof document !== 'undefined') {
                // 移除旧的屏幕类型类
                document.body.classList.remove('screen-mobile', 'screen-tablet', 'screen-desktop', 'screen-large');
                // 添加当前屏幕类型类
                document.body.classList.add(`screen-${this.device.screenType}`);
                // 添加方向类
                document.body.classList.remove('orientation-portrait', 'orientation-landscape');
                document.body.classList.add(`orientation-${this.device.orientation}`);
            }
            // #endif
        } catch (error) {
            console.error('MultiPlatformAdapter - Responsive styles failed:', error);
        }
    }

    /**
     * 触发回调函数
     */
    triggerCallbacks(type, data) {
        if (this.callbacks[type]) {
            this.callbacks[type].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`MultiPlatformAdapter - ${type} callback error:`, error);
                }
            });
        }
    }

    /**
     * 注册事件回调
     */
    on(event, callback) {
        if (this.callbacks[event] && typeof callback === 'function') {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * 移除事件回调
     */
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    // ==================== 公共API方法 ====================

    /**
     * 获取完整的适配信息
     */
    getAdapterInfo() {
        return {
            platform: { ...this.platform },
            device: { ...this.device },
            config: { ...this.config },
            windowInfo: this.windowInfo,
            deviceInfo: this.deviceInfo
        };
    }

    /**
     * 获取当前平台信息
     */
    getPlatform() {
        return this.platform.current;
    }

    /**
     * 获取设备类型
     */
    getDeviceType() {
        if (this.device.isDesktop) return 'desktop';
        if (this.device.isTablet) return 'tablet';
        return 'mobile';
    }

    /**
     * 获取屏幕类型
     */
    getScreenType() {
        return this.device.screenType;
    }

    /**
     * 检查是否为指定平台
     */
    isPlatform(platform) {
        return this.platform.current === platform || this.platform[`is${platform.charAt(0).toUpperCase() + platform.slice(1)}`];
    }

    /**
     * 检查是否为PC端
     */
    isPC() {
        return this.platform.isPC;
    }

    /**
     * 检查是否为移动端
     */
    isMobile() {
        return this.device.isMobile && !this.platform.isPC;
    }

    /**
     * 检查是否为平板
     */
    isTablet() {
        return this.device.isTablet;
    }

    /**
     * 检查是否为桌面端
     */
    isDesktop() {
        return this.device.isDesktop;
    }

    /**
     * 检查是否为指定屏幕类型
     */
    isScreenType(type) {
        return this.device.screenType === type;
    }

    /**
     * 获取响应式值
     */
    getResponsiveValue(values) {
        if (typeof values !== 'object' || values === null) {
            return values;
        }

        // 按优先级返回对应屏幕类型的值
        return values[this.device.screenType] ||
               values.desktop ||
               values.tablet ||
               values.mobile ||
               values.default ||
               values;
    }

    /**
     * 设置PC缩放比例
     */
    setPCScaleRatio(ratio) {
        if (ratio >= this.config.pcScale.minRatio && ratio <= this.config.pcScale.maxRatio) {
            this.config.pcScale.ratio = ratio;
            if (this.shouldApplyPCScale()) {
                this.applyPCScale();
            }
        }
    }

    /**
     * 获取PC缩放比例
     */
    getPCScaleRatio() {
        return this.config.pcScale.ratio;
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.applyAdaptation();
    }

    /**
     * 刷新适配
     */
    refresh() {
        this.detectDevice();
        this.detectScreenType();
        this.detectOrientation();
        this.applyAdaptation();
    }

    /**
     * 销毁适配器
     */
    destroy() {
        // 清理事件监听器
        // #ifdef H5
        if (typeof window !== 'undefined') {
            // 移除事件监听器的逻辑
        }
        // #endif

        // 清理回调函数
        Object.keys(this.callbacks).forEach(key => {
            this.callbacks[key] = [];
        });

        console.log('MultiPlatformAdapter destroyed');
    }
}

// 创建全局实例
const multiPlatformAdapter = new MultiPlatformAdapter();

// 兼容旧的API
const pcAdapter = multiPlatformAdapter;

export default multiPlatformAdapter;
export { pcAdapter };
}

// 创建全局实例
const pcAdapter = new PCAdapter();

export default pcAdapter;
