/**
 * PC端适配核心模块
 * 专注于PC端检测和缩放适配
 */

class PCAdapter {
    constructor() {
        this.isPC = false;
        this.isPCWeixin = false;
        this.windowInfo = null;
        this.deviceInfo = null;
        this.resizeCallbacks = [];
        this.scaleRatio = 0.8; // PC端缩放比例

        this.init();
    }

    /**
     * 初始化PC适配器
     */
    init() {
        this.detectDevice();
        this.setupWindowListener();
        this.applyPCStyles();
    }

    /**
     * 检测设备类型
     */
    detectDevice() {
        try {
            // 获取设备信息
            this.deviceInfo = uni.getDeviceInfo();
            this.windowInfo = uni.getWindowInfo();
            
            // 检测是否为PC端
            const platform = this.deviceInfo.platform;
            const userAgent = this.deviceInfo.userAgent || '';
            
            // PC端判断逻辑
            this.isPC = platform === 'windows' || 
                       platform === 'mac' || 
                       platform === 'linux' ||
                       /Windows|Mac|Linux/.test(userAgent);
            
            // 微信PC端检测
            // #ifdef MP-WEIXIN
            this.isPCWeixin = this.isPC && /MicroMessenger/.test(userAgent);
            // #endif
            
            console.log('PC Adapter - Device detected:', {
                isPC: this.isPC,
                isPCWeixin: this.isPCWeixin,
                platform: platform,
                windowWidth: this.windowInfo.windowWidth,
                windowHeight: this.windowInfo.windowHeight
            });
            
        } catch (error) {
            console.error('PC Adapter - Device detection failed:', error);
            this.isPC = false;
            this.isPCWeixin = false;
        }
    }

    /**
     * 设置窗口变化监听
     */
    setupWindowListener() {
        // #ifdef MP-WEIXIN
        if (this.isPCWeixin) {
            try {
                wx.onWindowResize((res) => {
                    console.log('PC Adapter - Window resized:', res);
                    this.windowInfo = {
                        ...this.windowInfo,
                        windowWidth: res.size.windowWidth,
                        windowHeight: res.size.windowHeight
                    };
                    this.triggerResizeCallbacks(res);
                });
            } catch (error) {
                console.error('PC Adapter - Window listener setup failed:', error);
            }
        }
        // #endif

        // H5端窗口监听
        // #ifdef H5
        if (typeof window !== 'undefined') {
            window.addEventListener('resize', () => {
                this.windowInfo = uni.getWindowInfo();
                this.triggerResizeCallbacks({
                    size: {
                        windowWidth: this.windowInfo.windowWidth,
                        windowHeight: this.windowInfo.windowHeight
                    }
                });
            });
        }
        // #endif
    }

    /**
     * 应用PC端样式
     */
    applyPCStyles() {
        if (!this.isPC) return;

        try {
            // #ifdef H5
            if (typeof document !== 'undefined') {
                // 添加PC端样式类
                document.body.classList.add('pc-mode');

                // 创建PC端样式
                const style = document.createElement('style');
                style.textContent = `
                    .pc-mode {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 100vh;
                        margin: 0;
                        padding: 20px;
                    }
                    .pc-mode .uni-app {
                        width: 375px !important;
                        height: 667px !important;
                        background: #fff;
                        border-radius: 12px;
                        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                        overflow: hidden;
                        transform: scale(${this.scaleRatio});
                        transform-origin: center;
                        position: relative;
                    }
                `;
                document.head.appendChild(style);
            }
            // #endif

            console.log('PC Adapter - PC styles applied');
        } catch (error) {
            console.error('PC Adapter - Failed to apply PC styles:', error);
        }
    }

    /**
     * 触发窗口变化回调
     */
    triggerResizeCallbacks(resizeData) {
        this.resizeCallbacks.forEach(callback => {
            try {
                callback({
                    ...resizeData,
                    isPC: this.isPC,
                    isPCWeixin: this.isPCWeixin,
                    scaleRatio: this.scaleRatio
                });
            } catch (error) {
                console.error('PC Adapter - Resize callback error:', error);
            }
        });
    }

    /**
     * 注册窗口变化回调
     */
    onWindowResize(callback) {
        if (typeof callback === 'function') {
            this.resizeCallbacks.push(callback);
        }
    }

    /**
     * 移除窗口变化回调
     */
    offWindowResize(callback) {
        const index = this.resizeCallbacks.indexOf(callback);
        if (index > -1) {
            this.resizeCallbacks.splice(index, 1);
        }
    }

    /**
     * 获取当前设备信息
     */
    getDeviceInfo() {
        return {
            isPC: this.isPC,
            isPCWeixin: this.isPCWeixin,
            platform: this.deviceInfo?.platform,
            windowWidth: this.windowInfo?.windowWidth,
            windowHeight: this.windowInfo?.windowHeight,
            scaleRatio: this.scaleRatio,
            deviceInfo: this.deviceInfo,
            windowInfo: this.windowInfo
        };
    }

    /**
     * 设置缩放比例
     */
    setScaleRatio(ratio) {
        this.scaleRatio = ratio;
        if (this.isPC) {
            this.applyPCStyles();
        }
    }

    /**
     * 检查是否为PC端
     */
    isPCDevice() {
        return this.isPC;
    }

    /**
     * 检查是否为微信PC端
     */
    isPCWeixinDevice() {
        return this.isPCWeixin;
    }
}

// 创建全局实例
const pcAdapter = new PCAdapter();

export default pcAdapter;
