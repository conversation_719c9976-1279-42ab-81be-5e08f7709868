/**
 * PC端适配核心模块
 * 提供PC端检测、窗口监听和响应式布局功能
 */

class PCAdapter {
    constructor() {
        this.isPC = false;
        this.isPCWeixin = false;
        this.windowInfo = null;
        this.deviceInfo = null;
        this.resizeCallbacks = [];
        this.currentBreakpoint = 'mobile';
        this.breakpoints = {
            mobile: { min: 0, max: 767 },
            tablet: { min: 768, max: 1023 },
            desktop: { min: 1024, max: 1439 },
            large: { min: 1440, max: Infinity }
        };
        
        this.init();
    }

    /**
     * 初始化PC适配器
     */
    init() {
        this.detectDevice();
        this.setupWindowListener();
        this.updateBreakpoint();
    }

    /**
     * 检测设备类型
     */
    detectDevice() {
        try {
            // 获取设备信息
            this.deviceInfo = uni.getDeviceInfo();
            this.windowInfo = uni.getWindowInfo();
            
            // 检测是否为PC端
            const platform = this.deviceInfo.platform;
            const userAgent = this.deviceInfo.userAgent || '';
            
            // PC端判断逻辑
            this.isPC = platform === 'windows' || 
                       platform === 'mac' || 
                       platform === 'linux' ||
                       /Windows|Mac|Linux/.test(userAgent);
            
            // 微信PC端检测
            // #ifdef MP-WEIXIN
            this.isPCWeixin = this.isPC && /MicroMessenger/.test(userAgent);
            // #endif
            
            console.log('PC Adapter - Device detected:', {
                isPC: this.isPC,
                isPCWeixin: this.isPCWeixin,
                platform: platform,
                windowWidth: this.windowInfo.windowWidth,
                windowHeight: this.windowInfo.windowHeight
            });
            
        } catch (error) {
            console.error('PC Adapter - Device detection failed:', error);
            this.isPC = false;
            this.isPCWeixin = false;
        }
    }

    /**
     * 设置窗口变化监听
     */
    setupWindowListener() {
        // #ifdef MP-WEIXIN
        if (this.isPCWeixin) {
            try {
                wx.onWindowResize((res) => {
                    console.log('PC Adapter - Window resized:', res);
                    this.windowInfo = {
                        ...this.windowInfo,
                        windowWidth: res.size.windowWidth,
                        windowHeight: res.size.windowHeight
                    };
                    this.updateBreakpoint();
                    this.triggerResizeCallbacks(res);
                });
            } catch (error) {
                console.error('PC Adapter - Window listener setup failed:', error);
            }
        }
        // #endif
        
        // H5端窗口监听
        // #ifdef H5
        if (typeof window !== 'undefined') {
            window.addEventListener('resize', () => {
                this.windowInfo = uni.getWindowInfo();
                this.updateBreakpoint();
                this.triggerResizeCallbacks({
                    size: {
                        windowWidth: this.windowInfo.windowWidth,
                        windowHeight: this.windowInfo.windowHeight
                    }
                });
            });
        }
        // #endif
    }

    /**
     * 更新当前断点
     */
    updateBreakpoint() {
        if (!this.windowInfo) return;
        
        const width = this.windowInfo.windowWidth;
        let newBreakpoint = 'mobile';
        
        for (const [name, range] of Object.entries(this.breakpoints)) {
            if (width >= range.min && width <= range.max) {
                newBreakpoint = name;
                break;
            }
        }
        
        if (newBreakpoint !== this.currentBreakpoint) {
            const oldBreakpoint = this.currentBreakpoint;
            this.currentBreakpoint = newBreakpoint;
            console.log(`PC Adapter - Breakpoint changed: ${oldBreakpoint} -> ${newBreakpoint}`);
        }
    }

    /**
     * 触发窗口变化回调
     */
    triggerResizeCallbacks(resizeData) {
        this.resizeCallbacks.forEach(callback => {
            try {
                callback({
                    ...resizeData,
                    breakpoint: this.currentBreakpoint,
                    isPC: this.isPC,
                    isPCWeixin: this.isPCWeixin
                });
            } catch (error) {
                console.error('PC Adapter - Resize callback error:', error);
            }
        });
    }

    /**
     * 注册窗口变化回调
     */
    onWindowResize(callback) {
        if (typeof callback === 'function') {
            this.resizeCallbacks.push(callback);
        }
    }

    /**
     * 移除窗口变化回调
     */
    offWindowResize(callback) {
        const index = this.resizeCallbacks.indexOf(callback);
        if (index > -1) {
            this.resizeCallbacks.splice(index, 1);
        }
    }

    /**
     * 获取当前设备信息
     */
    getDeviceInfo() {
        return {
            isPC: this.isPC,
            isPCWeixin: this.isPCWeixin,
            platform: this.deviceInfo?.platform,
            breakpoint: this.currentBreakpoint,
            windowWidth: this.windowInfo?.windowWidth,
            windowHeight: this.windowInfo?.windowHeight,
            deviceInfo: this.deviceInfo,
            windowInfo: this.windowInfo
        };
    }

    /**
     * 检查是否为指定断点
     */
    isBreakpoint(breakpoint) {
        return this.currentBreakpoint === breakpoint;
    }

    /**
     * 检查是否为移动端
     */
    isMobile() {
        return this.currentBreakpoint === 'mobile';
    }

    /**
     * 检查是否为平板
     */
    isTablet() {
        return this.currentBreakpoint === 'tablet';
    }

    /**
     * 检查是否为桌面端
     */
    isDesktop() {
        return this.currentBreakpoint === 'desktop' || this.currentBreakpoint === 'large';
    }

    /**
     * 获取响应式值
     */
    getResponsiveValue(values) {
        if (typeof values !== 'object') return values;
        
        // 按优先级返回对应断点的值
        return values[this.currentBreakpoint] || 
               values.desktop || 
               values.tablet || 
               values.mobile || 
               values.default;
    }

    /**
     * 获取响应式样式
     */
    getResponsiveStyle(styles) {
        if (typeof styles !== 'object') return styles;
        
        const currentStyle = styles[this.currentBreakpoint] || {};
        const baseStyle = styles.base || {};
        
        return { ...baseStyle, ...currentStyle };
    }
}

// 创建全局实例
const pcAdapter = new PCAdapter();

export default pcAdapter;
