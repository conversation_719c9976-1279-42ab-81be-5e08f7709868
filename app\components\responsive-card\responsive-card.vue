<template>
  <view class="responsive-card" :class="cardClasses" :style="cardStyles" @click="handleClick">
    <!-- 卡片头部 -->
    <view v-if="$slots.header || title" class="responsive-card__header">
      <slot name="header">
        <text class="responsive-card__title">{{ title }}</text>
        <text v-if="subtitle" class="responsive-card__subtitle">{{ subtitle }}</text>
      </slot>
    </view>
    
    <!-- 卡片内容 -->
    <view class="responsive-card__body">
      <slot></slot>
    </view>
    
    <!-- 卡片底部 -->
    <view v-if="$slots.footer" class="responsive-card__footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
import multiPlatformAdapter from '@/common/js/pc-adapter.js'

export default {
  name: 'ResponsiveCard',
  props: {
    // 卡片标题
    title: {
      type: String,
      default: ''
    },
    
    // 卡片副标题
    subtitle: {
      type: String,
      default: ''
    },
    
    // 内边距配置
    padding: {
      type: [String, Number, Object],
      default: () => ({
        mobile: '16px',
        tablet: '20px',
        desktop: '24px',
        large: '28px'
      })
    },
    
    // 外边距配置
    margin: {
      type: [String, Number, Object],
      default: null
    },
    
    // 边框圆角配置
    radius: {
      type: [String, Number, Object],
      default: () => ({
        mobile: '8px',
        tablet: '10px',
        desktop: '12px',
        large: '14px'
      })
    },
    
    // 阴影配置
    shadow: {
      type: [Boolean, String, Object],
      default: () => ({
        mobile: 'sm',
        tablet: 'md',
        desktop: 'lg',
        large: 'xl'
      })
    },
    
    // 背景配置
    background: {
      type: [String, Object],
      default: '#fff'
    },
    
    // 边框配置
    border: {
      type: [String, Object],
      default: null
    },
    
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    
    // 是否启用悬停效果
    hoverable: {
      type: Boolean,
      default: true
    },
    
    // PC端优化
    pcOptimized: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      adapterInfo: {
        platform: {},
        device: {},
        screenType: 'mobile'
      }
    }
  },
  
  computed: {
    cardClasses() {
      const classes = ['responsive-card']
      
      // 屏幕类型类
      classes.push('responsive-card--' + this.adapterInfo.device.screenType)

      // 平台类
      if (this.adapterInfo.platform.current) {
        classes.push('responsive-card--platform-' + this.adapterInfo.platform.current)
      }
      
      // 功能类
      if (this.clickable) {
        classes.push('responsive-card--clickable')
      }
      
      if (this.hoverable && this.adapterInfo.platform.isPC) {
        classes.push('responsive-card--hoverable')
      }
      
      if (this.pcOptimized && this.adapterInfo.platform.isPC) {
        classes.push('responsive-card--pc-optimized')
      }
      
      // 阴影类
      const shadowValue = this.getResponsiveValue(this.shadow)
      if (shadowValue) {
        if (typeof shadowValue === 'boolean' && shadowValue) {
          classes.push('responsive-card--shadow')
        } else if (typeof shadowValue === 'string') {
          classes.push(`responsive-card--shadow-${shadowValue}`)
        }
      }
      
      return classes
    },
    
    cardStyles() {
      const styles = {}
      
      // 内边距
      const padding = this.getResponsiveValue(this.padding)
      if (padding) {
        styles.padding = this.formatValue(padding)
      }
      
      // 外边距
      const margin = this.getResponsiveValue(this.margin)
      if (margin) {
        styles.margin = this.formatValue(margin)
      }
      
      // 边框圆角
      const radius = this.getResponsiveValue(this.radius)
      if (radius) {
        styles.borderRadius = this.formatValue(radius)
      }
      
      // 背景
      const background = this.getResponsiveValue(this.background)
      if (background) {
        if (typeof background === 'string') {
          styles.backgroundColor = background
        } else if (typeof background === 'object') {
          Object.assign(styles, background)
        }
      }
      
      // 边框
      const border = this.getResponsiveValue(this.border)
      if (border) {
        styles.border = border
      }
      
      return styles
    }
  },
  
  mounted() {
    this.updateAdapterInfo()
    this.setupEventListeners()
  },
  
  beforeDestroy() {
    this.removeEventListeners()
  },
  
  methods: {
    updateAdapterInfo() {
      this.adapterInfo = multiPlatformAdapter.getAdapterInfo()
    },
    
    setupEventListeners() {
      this.resizeHandler = () => {
        this.updateAdapterInfo()
      }
      
      multiPlatformAdapter.on('resize', this.resizeHandler)
    },
    
    removeEventListeners() {
      if (this.resizeHandler) {
        multiPlatformAdapter.off('resize', this.resizeHandler)
      }
    },
    
    getResponsiveValue(value) {
      return multiPlatformAdapter.getResponsiveValue(value)
    },
    
    formatValue(value) {
      if (typeof value === 'number') {
        return `${value}px`
      }
      return value
    },
    
    handleClick(event) {
      if (this.clickable) {
        this.$emit('click', event)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/pc-responsive.scss';

.responsive-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  
  &__header {
    padding: 16px 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
  }
  
  &__title {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }
  
  &__subtitle {
    display: block;
    font-size: 14px;
    color: #666;
  }
  
  &__body {
    padding: 16px;
  }
  
  &__footer {
    padding: 0 16px 16px;
    border-top: 1px solid #f0f0f0;
    margin-top: 16px;
  }
  
  // 可点击样式
  &--clickable {
    cursor: pointer;
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  // 悬停效果
  &--hoverable {
    @include pc-only {
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }
    }
  }
  
  // PC端优化
  &--pc-optimized {
    @include pc-only {
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
  }
  
  // 阴影样式
  &--shadow-sm {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }
  
  &--shadow-md {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &--shadow-lg {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }
  
  &--shadow-xl {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  }
  
  // 响应式样式
  &--mobile {
    @include respond-to(mobile) {
      border-radius: 8px;
      
      .responsive-card__header,
      .responsive-card__body,
      .responsive-card__footer {
        padding-left: 16px;
        padding-right: 16px;
      }
    }
  }
  
  &--tablet {
    @include respond-to(tablet) {
      border-radius: 10px;
      
      .responsive-card__header,
      .responsive-card__body,
      .responsive-card__footer {
        padding-left: 20px;
        padding-right: 20px;
      }
    }
  }
  
  &--desktop {
    @include respond-to(desktop) {
      border-radius: 12px;
      
      .responsive-card__header,
      .responsive-card__body,
      .responsive-card__footer {
        padding-left: 24px;
        padding-right: 24px;
      }
    }
  }
  
  &--large {
    @include respond-to(large) {
      border-radius: 14px;
      
      .responsive-card__header,
      .responsive-card__body,
      .responsive-card__footer {
        padding-left: 28px;
        padding-right: 28px;
      }
    }
  }
}
</style>
