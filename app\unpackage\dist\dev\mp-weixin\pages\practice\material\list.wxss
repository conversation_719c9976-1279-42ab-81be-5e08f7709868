.bottom-layout {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
}
@charset "UTF-8";
/**
 * uni-app全局样式 - PC端适配版本
 */
/**
 * PC端适配样式
 * 实现小程序在PC端的缩放适配
 */
@media (min-width: 1024px) {
.pc-app-container.data-v-487aa56b {
    width: 420px;
    margin: 0 auto;
    -webkit-transform-origin: top center;
            transform-origin: top center;
    -webkit-transform: scale(0.75);
            transform: scale(0.75);
    background: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    min-height: 100vh;
}
}
@media (max-width: 1023px) {
.pc-app-container.data-v-487aa56b {
    width: 100%;
    margin: 0;
    -webkit-transform: none;
            transform: none;
    box-shadow: none;
    border-radius: 0;
}
}
@media (min-width: 1024px) {
.pc-page-background.data-v-487aa56b {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 40px 20px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}
}
@media (max-width: 1023px) {
.pc-page-background.data-v-487aa56b {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 0;
}
}
@media (min-width: 1024px) {
.pc-scale-container.data-v-487aa56b {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.pc-scale-container .pc-app-wrapper.data-v-487aa56b {
    width: 375px;
    height: 667px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    -webkit-transform: scale(0.75);
            transform: scale(0.75);
    -webkit-transform-origin: center;
            transform-origin: center;
    position: relative;
}
}
@media (max-width: 1023px) {
.pc-scale-container.data-v-487aa56b {
    width: 100%;
    height: 100vh;
}
.pc-scale-container .pc-app-wrapper.data-v-487aa56b {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    -webkit-transform: none;
            transform: none;
}
}
@media (max-width: 1023px) {
.pc-only.data-v-487aa56b {
    display: none !important;
}
}
@media (min-width: 1024px) {
.mobile-only.data-v-487aa56b {
    display: none !important;
}
}
@media (min-width: 1024px) {
page.data-v-487aa56b, page.data-v-487aa56b {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}
.uni-page-head.data-v-487aa56b {
    display: none !important;
}
.uni-page-wrapper.data-v-487aa56b {
    height: 100vh !important;
}
}
page.data-v-487aa56b {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}
@media (min-width: 1024px) {
page.data-v-487aa56b {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}
.uni-page-wrapper.data-v-487aa56b {
    width: 375px !important;
    height: 667px !important;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
    -webkit-transform-origin: center;
            transform-origin: center;
    position: relative;
}
.uni-tabbar.data-v-487aa56b {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
}
}
page.data-v-487aa56b {
  height: 100%;
  background-color: #f5f7fa;
}
.material-list-ios.data-v-487aa56b {
  background: #f7f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.list-content.data-v-487aa56b {
  flex: 1;
  padding: 32rpx 0 120rpx 0;
}
.scroll-area.data-v-487aa56b {
  min-height: 60vh;
}
.material-card.data-v-487aa56b {
  background: #fff;
  border-radius: 24rpx;
  margin: 32rpx 32rpx 0 32rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  box-shadow: 0 4rpx 32rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.card-main.data-v-487aa56b {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.card-title-row.data-v-487aa56b {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.card-title.data-v-487aa56b {
  font-size: 36rpx;
  font-weight: 700;
  color: #222;
}
.card-tags.data-v-487aa56b {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}
.price-tag.data-v-487aa56b {
  font-size: 28rpx;
  padding: 8rpx 24rpx;
}
.version-tag.data-v-487aa56b {
  font-size: 22rpx;
  padding: 4rpx 16rpx;
}
.card-meta-row.data-v-487aa56b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
}
.meta-item.data-v-487aa56b {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  font-size: 26rpx;
  color: #888;
}
.center-value.data-v-487aa56b {
  display: flex;
  align-items: center;
  height: 100%;
}
.meta-label.data-v-487aa56b {
  margin-left: 4rpx;
}
.meta-value.data-v-487aa56b {
  color: #39b54a;
  font-weight: 600;
  margin-left: 8rpx;
}
.meta-action.data-v-487aa56b {
  display: flex;
  align-items: center;
}
.detail-btn.data-v-487aa56b {
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  font-weight: 500;
  border-radius: 16rpx;
}
.float-action-container.data-v-487aa56b {
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  z-index: 99;
}
.share-button.data-v-487aa56b {
  background: linear-gradient(135deg, #0081ff, #1cbbb4);
  border-radius: 50rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 129, 255, 0.3);
  padding: 16rpx 32rpx;
  display: flex;
  align-items: center;
}
.share-button text.data-v-487aa56b {
  color: #ffffff;
}
.share-button .cuIcon-add.data-v-487aa56b {
  font-size: 32rpx;
  margin-right: 8rpx;
}

