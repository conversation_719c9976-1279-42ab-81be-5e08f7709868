.bottom-layout {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
}
@charset "UTF-8";
/**
 * uni-app全局样式
 */
/**
 * PC端响应式样式
 * 提供响应式断点、工具类和PC端优化样式
 */
.pc-container.data-v-487aa56b, .container.data-v-487aa56b {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}
@media (min-width: 768px) {
.pc-container.data-v-487aa56b, .container.data-v-487aa56b {
    max-width: 750px;
    padding: 0 24px;
}
}
@media (min-width: 1024px) {
.pc-container.data-v-487aa56b, .container.data-v-487aa56b {
    max-width: 1000px;
    padding: 0 32px;
}
}
@media (min-width: 1440px) {
.pc-container.data-v-487aa56b, .container.data-v-487aa56b {
    max-width: 1200px;
    padding: 0 40px;
}
}
.pc-card.data-v-487aa56b, .card.data-v-487aa56b {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
@media (min-width: 1024px) {
.pc-card.data-v-487aa56b, .card.data-v-487aa56b {
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
}
.pc-row.data-v-487aa56b {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}
@media (min-width: 768px) {
.pc-row.data-v-487aa56b {
    margin: 0 -12px;
}
}
@media (min-width: 1024px) {
.pc-row.data-v-487aa56b {
    margin: 0 -16px;
}
}
.pc-col.data-v-487aa56b {
  padding: 0 8px;
}
@media (min-width: 768px) {
.pc-col.data-v-487aa56b {
    padding: 0 12px;
}
}
@media (min-width: 1024px) {
.pc-col.data-v-487aa56b {
    padding: 0 16px;
}
}
.pc-col-1.data-v-487aa56b {
  flex: 0 0 8.33333%;
  max-width: 8.33333%;
}
@media (min-width: 768px) {
.pc-col-tablet-1.data-v-487aa56b {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-1.data-v-487aa56b {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
}
}
@media (min-width: 1440px) {
.pc-col-large-1.data-v-487aa56b {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
}
}
.pc-col-2.data-v-487aa56b {
  flex: 0 0 16.66667%;
  max-width: 16.66667%;
}
@media (min-width: 768px) {
.pc-col-tablet-2.data-v-487aa56b {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-2.data-v-487aa56b {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
}
}
@media (min-width: 1440px) {
.pc-col-large-2.data-v-487aa56b {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
}
}
.pc-col-3.data-v-487aa56b {
  flex: 0 0 25%;
  max-width: 25%;
}
@media (min-width: 768px) {
.pc-col-tablet-3.data-v-487aa56b {
    flex: 0 0 25%;
    max-width: 25%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-3.data-v-487aa56b {
    flex: 0 0 25%;
    max-width: 25%;
}
}
@media (min-width: 1440px) {
.pc-col-large-3.data-v-487aa56b {
    flex: 0 0 25%;
    max-width: 25%;
}
}
.pc-col-4.data-v-487aa56b {
  flex: 0 0 33.33333%;
  max-width: 33.33333%;
}
@media (min-width: 768px) {
.pc-col-tablet-4.data-v-487aa56b {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-4.data-v-487aa56b {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
}
}
@media (min-width: 1440px) {
.pc-col-large-4.data-v-487aa56b {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
}
}
.pc-col-5.data-v-487aa56b {
  flex: 0 0 41.66667%;
  max-width: 41.66667%;
}
@media (min-width: 768px) {
.pc-col-tablet-5.data-v-487aa56b {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-5.data-v-487aa56b {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
}
}
@media (min-width: 1440px) {
.pc-col-large-5.data-v-487aa56b {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
}
}
.pc-col-6.data-v-487aa56b {
  flex: 0 0 50%;
  max-width: 50%;
}
@media (min-width: 768px) {
.pc-col-tablet-6.data-v-487aa56b {
    flex: 0 0 50%;
    max-width: 50%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-6.data-v-487aa56b {
    flex: 0 0 50%;
    max-width: 50%;
}
}
@media (min-width: 1440px) {
.pc-col-large-6.data-v-487aa56b {
    flex: 0 0 50%;
    max-width: 50%;
}
}
.pc-col-7.data-v-487aa56b {
  flex: 0 0 58.33333%;
  max-width: 58.33333%;
}
@media (min-width: 768px) {
.pc-col-tablet-7.data-v-487aa56b {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-7.data-v-487aa56b {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
}
}
@media (min-width: 1440px) {
.pc-col-large-7.data-v-487aa56b {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
}
}
.pc-col-8.data-v-487aa56b {
  flex: 0 0 66.66667%;
  max-width: 66.66667%;
}
@media (min-width: 768px) {
.pc-col-tablet-8.data-v-487aa56b {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-8.data-v-487aa56b {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
}
}
@media (min-width: 1440px) {
.pc-col-large-8.data-v-487aa56b {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
}
}
.pc-col-9.data-v-487aa56b {
  flex: 0 0 75%;
  max-width: 75%;
}
@media (min-width: 768px) {
.pc-col-tablet-9.data-v-487aa56b {
    flex: 0 0 75%;
    max-width: 75%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-9.data-v-487aa56b {
    flex: 0 0 75%;
    max-width: 75%;
}
}
@media (min-width: 1440px) {
.pc-col-large-9.data-v-487aa56b {
    flex: 0 0 75%;
    max-width: 75%;
}
}
.pc-col-10.data-v-487aa56b {
  flex: 0 0 83.33333%;
  max-width: 83.33333%;
}
@media (min-width: 768px) {
.pc-col-tablet-10.data-v-487aa56b {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-10.data-v-487aa56b {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
}
}
@media (min-width: 1440px) {
.pc-col-large-10.data-v-487aa56b {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
}
}
.pc-col-11.data-v-487aa56b {
  flex: 0 0 91.66667%;
  max-width: 91.66667%;
}
@media (min-width: 768px) {
.pc-col-tablet-11.data-v-487aa56b {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-11.data-v-487aa56b {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
}
}
@media (min-width: 1440px) {
.pc-col-large-11.data-v-487aa56b {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
}
}
.pc-col-12.data-v-487aa56b {
  flex: 0 0 100%;
  max-width: 100%;
}
@media (min-width: 768px) {
.pc-col-tablet-12.data-v-487aa56b {
    flex: 0 0 100%;
    max-width: 100%;
}
}
@media (min-width: 1024px) {
.pc-col-desktop-12.data-v-487aa56b {
    flex: 0 0 100%;
    max-width: 100%;
}
}
@media (min-width: 1440px) {
.pc-col-large-12.data-v-487aa56b {
    flex: 0 0 100%;
    max-width: 100%;
}
}
.pc-p-0.data-v-487aa56b {
  padding: 0px !important;
}
.pc-pt-0.data-v-487aa56b {
  padding-top: 0px !important;
}
.pc-pr-0.data-v-487aa56b {
  padding-right: 0px !important;
}
.pc-pb-0.data-v-487aa56b {
  padding-bottom: 0px !important;
}
.pc-pl-0.data-v-487aa56b {
  padding-left: 0px !important;
}
.pc-px-0.data-v-487aa56b {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.pc-py-0.data-v-487aa56b {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.pc-m-0.data-v-487aa56b {
  margin: 0px !important;
}
.pc-mt-0.data-v-487aa56b {
  margin-top: 0px !important;
}
.pc-mr-0.data-v-487aa56b {
  margin-right: 0px !important;
}
.pc-mb-0.data-v-487aa56b {
  margin-bottom: 0px !important;
}
.pc-ml-0.data-v-487aa56b {
  margin-left: 0px !important;
}
.pc-mx-0.data-v-487aa56b {
  margin-left: 0px !important;
  margin-right: 0px !important;
}
.pc-my-0.data-v-487aa56b {
  margin-top: 0px !important;
  margin-bottom: 0px !important;
}
.pc-p-4.data-v-487aa56b {
  padding: 4px !important;
}
.pc-pt-4.data-v-487aa56b {
  padding-top: 4px !important;
}
.pc-pr-4.data-v-487aa56b {
  padding-right: 4px !important;
}
.pc-pb-4.data-v-487aa56b {
  padding-bottom: 4px !important;
}
.pc-pl-4.data-v-487aa56b {
  padding-left: 4px !important;
}
.pc-px-4.data-v-487aa56b {
  padding-left: 4px !important;
  padding-right: 4px !important;
}
.pc-py-4.data-v-487aa56b {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}
.pc-m-4.data-v-487aa56b {
  margin: 4px !important;
}
.pc-mt-4.data-v-487aa56b {
  margin-top: 4px !important;
}
.pc-mr-4.data-v-487aa56b {
  margin-right: 4px !important;
}
.pc-mb-4.data-v-487aa56b {
  margin-bottom: 4px !important;
}
.pc-ml-4.data-v-487aa56b {
  margin-left: 4px !important;
}
.pc-mx-4.data-v-487aa56b {
  margin-left: 4px !important;
  margin-right: 4px !important;
}
.pc-my-4.data-v-487aa56b {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
}
.pc-p-8.data-v-487aa56b {
  padding: 8px !important;
}
.pc-pt-8.data-v-487aa56b {
  padding-top: 8px !important;
}
.pc-pr-8.data-v-487aa56b {
  padding-right: 8px !important;
}
.pc-pb-8.data-v-487aa56b {
  padding-bottom: 8px !important;
}
.pc-pl-8.data-v-487aa56b {
  padding-left: 8px !important;
}
.pc-px-8.data-v-487aa56b {
  padding-left: 8px !important;
  padding-right: 8px !important;
}
.pc-py-8.data-v-487aa56b {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}
.pc-m-8.data-v-487aa56b {
  margin: 8px !important;
}
.pc-mt-8.data-v-487aa56b {
  margin-top: 8px !important;
}
.pc-mr-8.data-v-487aa56b {
  margin-right: 8px !important;
}
.pc-mb-8.data-v-487aa56b {
  margin-bottom: 8px !important;
}
.pc-ml-8.data-v-487aa56b {
  margin-left: 8px !important;
}
.pc-mx-8.data-v-487aa56b {
  margin-left: 8px !important;
  margin-right: 8px !important;
}
.pc-my-8.data-v-487aa56b {
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}
.pc-p-12.data-v-487aa56b {
  padding: 12px !important;
}
.pc-pt-12.data-v-487aa56b {
  padding-top: 12px !important;
}
.pc-pr-12.data-v-487aa56b {
  padding-right: 12px !important;
}
.pc-pb-12.data-v-487aa56b {
  padding-bottom: 12px !important;
}
.pc-pl-12.data-v-487aa56b {
  padding-left: 12px !important;
}
.pc-px-12.data-v-487aa56b {
  padding-left: 12px !important;
  padding-right: 12px !important;
}
.pc-py-12.data-v-487aa56b {
  padding-top: 12px !important;
  padding-bottom: 12px !important;
}
.pc-m-12.data-v-487aa56b {
  margin: 12px !important;
}
.pc-mt-12.data-v-487aa56b {
  margin-top: 12px !important;
}
.pc-mr-12.data-v-487aa56b {
  margin-right: 12px !important;
}
.pc-mb-12.data-v-487aa56b {
  margin-bottom: 12px !important;
}
.pc-ml-12.data-v-487aa56b {
  margin-left: 12px !important;
}
.pc-mx-12.data-v-487aa56b {
  margin-left: 12px !important;
  margin-right: 12px !important;
}
.pc-my-12.data-v-487aa56b {
  margin-top: 12px !important;
  margin-bottom: 12px !important;
}
.pc-p-16.data-v-487aa56b {
  padding: 16px !important;
}
.pc-pt-16.data-v-487aa56b {
  padding-top: 16px !important;
}
.pc-pr-16.data-v-487aa56b {
  padding-right: 16px !important;
}
.pc-pb-16.data-v-487aa56b {
  padding-bottom: 16px !important;
}
.pc-pl-16.data-v-487aa56b {
  padding-left: 16px !important;
}
.pc-px-16.data-v-487aa56b {
  padding-left: 16px !important;
  padding-right: 16px !important;
}
.pc-py-16.data-v-487aa56b {
  padding-top: 16px !important;
  padding-bottom: 16px !important;
}
.pc-m-16.data-v-487aa56b {
  margin: 16px !important;
}
.pc-mt-16.data-v-487aa56b {
  margin-top: 16px !important;
}
.pc-mr-16.data-v-487aa56b {
  margin-right: 16px !important;
}
.pc-mb-16.data-v-487aa56b {
  margin-bottom: 16px !important;
}
.pc-ml-16.data-v-487aa56b {
  margin-left: 16px !important;
}
.pc-mx-16.data-v-487aa56b {
  margin-left: 16px !important;
  margin-right: 16px !important;
}
.pc-my-16.data-v-487aa56b {
  margin-top: 16px !important;
  margin-bottom: 16px !important;
}
.pc-p-20.data-v-487aa56b {
  padding: 20px !important;
}
.pc-pt-20.data-v-487aa56b {
  padding-top: 20px !important;
}
.pc-pr-20.data-v-487aa56b {
  padding-right: 20px !important;
}
.pc-pb-20.data-v-487aa56b {
  padding-bottom: 20px !important;
}
.pc-pl-20.data-v-487aa56b {
  padding-left: 20px !important;
}
.pc-px-20.data-v-487aa56b {
  padding-left: 20px !important;
  padding-right: 20px !important;
}
.pc-py-20.data-v-487aa56b {
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}
.pc-m-20.data-v-487aa56b {
  margin: 20px !important;
}
.pc-mt-20.data-v-487aa56b {
  margin-top: 20px !important;
}
.pc-mr-20.data-v-487aa56b {
  margin-right: 20px !important;
}
.pc-mb-20.data-v-487aa56b {
  margin-bottom: 20px !important;
}
.pc-ml-20.data-v-487aa56b {
  margin-left: 20px !important;
}
.pc-mx-20.data-v-487aa56b {
  margin-left: 20px !important;
  margin-right: 20px !important;
}
.pc-my-20.data-v-487aa56b {
  margin-top: 20px !important;
  margin-bottom: 20px !important;
}
.pc-p-24.data-v-487aa56b {
  padding: 24px !important;
}
.pc-pt-24.data-v-487aa56b {
  padding-top: 24px !important;
}
.pc-pr-24.data-v-487aa56b {
  padding-right: 24px !important;
}
.pc-pb-24.data-v-487aa56b {
  padding-bottom: 24px !important;
}
.pc-pl-24.data-v-487aa56b {
  padding-left: 24px !important;
}
.pc-px-24.data-v-487aa56b {
  padding-left: 24px !important;
  padding-right: 24px !important;
}
.pc-py-24.data-v-487aa56b {
  padding-top: 24px !important;
  padding-bottom: 24px !important;
}
.pc-m-24.data-v-487aa56b {
  margin: 24px !important;
}
.pc-mt-24.data-v-487aa56b {
  margin-top: 24px !important;
}
.pc-mr-24.data-v-487aa56b {
  margin-right: 24px !important;
}
.pc-mb-24.data-v-487aa56b {
  margin-bottom: 24px !important;
}
.pc-ml-24.data-v-487aa56b {
  margin-left: 24px !important;
}
.pc-mx-24.data-v-487aa56b {
  margin-left: 24px !important;
  margin-right: 24px !important;
}
.pc-my-24.data-v-487aa56b {
  margin-top: 24px !important;
  margin-bottom: 24px !important;
}
.pc-p-32.data-v-487aa56b {
  padding: 32px !important;
}
.pc-pt-32.data-v-487aa56b {
  padding-top: 32px !important;
}
.pc-pr-32.data-v-487aa56b {
  padding-right: 32px !important;
}
.pc-pb-32.data-v-487aa56b {
  padding-bottom: 32px !important;
}
.pc-pl-32.data-v-487aa56b {
  padding-left: 32px !important;
}
.pc-px-32.data-v-487aa56b {
  padding-left: 32px !important;
  padding-right: 32px !important;
}
.pc-py-32.data-v-487aa56b {
  padding-top: 32px !important;
  padding-bottom: 32px !important;
}
.pc-m-32.data-v-487aa56b {
  margin: 32px !important;
}
.pc-mt-32.data-v-487aa56b {
  margin-top: 32px !important;
}
.pc-mr-32.data-v-487aa56b {
  margin-right: 32px !important;
}
.pc-mb-32.data-v-487aa56b {
  margin-bottom: 32px !important;
}
.pc-ml-32.data-v-487aa56b {
  margin-left: 32px !important;
}
.pc-mx-32.data-v-487aa56b {
  margin-left: 32px !important;
  margin-right: 32px !important;
}
.pc-my-32.data-v-487aa56b {
  margin-top: 32px !important;
  margin-bottom: 32px !important;
}
.pc-p-40.data-v-487aa56b {
  padding: 40px !important;
}
.pc-pt-40.data-v-487aa56b {
  padding-top: 40px !important;
}
.pc-pr-40.data-v-487aa56b {
  padding-right: 40px !important;
}
.pc-pb-40.data-v-487aa56b {
  padding-bottom: 40px !important;
}
.pc-pl-40.data-v-487aa56b {
  padding-left: 40px !important;
}
.pc-px-40.data-v-487aa56b {
  padding-left: 40px !important;
  padding-right: 40px !important;
}
.pc-py-40.data-v-487aa56b {
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}
.pc-m-40.data-v-487aa56b {
  margin: 40px !important;
}
.pc-mt-40.data-v-487aa56b {
  margin-top: 40px !important;
}
.pc-mr-40.data-v-487aa56b {
  margin-right: 40px !important;
}
.pc-mb-40.data-v-487aa56b {
  margin-bottom: 40px !important;
}
.pc-ml-40.data-v-487aa56b {
  margin-left: 40px !important;
}
.pc-mx-40.data-v-487aa56b {
  margin-left: 40px !important;
  margin-right: 40px !important;
}
.pc-my-40.data-v-487aa56b {
  margin-top: 40px !important;
  margin-bottom: 40px !important;
}
.pc-p-48.data-v-487aa56b {
  padding: 48px !important;
}
.pc-pt-48.data-v-487aa56b {
  padding-top: 48px !important;
}
.pc-pr-48.data-v-487aa56b {
  padding-right: 48px !important;
}
.pc-pb-48.data-v-487aa56b {
  padding-bottom: 48px !important;
}
.pc-pl-48.data-v-487aa56b {
  padding-left: 48px !important;
}
.pc-px-48.data-v-487aa56b {
  padding-left: 48px !important;
  padding-right: 48px !important;
}
.pc-py-48.data-v-487aa56b {
  padding-top: 48px !important;
  padding-bottom: 48px !important;
}
.pc-m-48.data-v-487aa56b {
  margin: 48px !important;
}
.pc-mt-48.data-v-487aa56b {
  margin-top: 48px !important;
}
.pc-mr-48.data-v-487aa56b {
  margin-right: 48px !important;
}
.pc-mb-48.data-v-487aa56b {
  margin-bottom: 48px !important;
}
.pc-ml-48.data-v-487aa56b {
  margin-left: 48px !important;
}
.pc-mx-48.data-v-487aa56b {
  margin-left: 48px !important;
  margin-right: 48px !important;
}
.pc-my-48.data-v-487aa56b {
  margin-top: 48px !important;
  margin-bottom: 48px !important;
}
.pc-p-56.data-v-487aa56b {
  padding: 56px !important;
}
.pc-pt-56.data-v-487aa56b {
  padding-top: 56px !important;
}
.pc-pr-56.data-v-487aa56b {
  padding-right: 56px !important;
}
.pc-pb-56.data-v-487aa56b {
  padding-bottom: 56px !important;
}
.pc-pl-56.data-v-487aa56b {
  padding-left: 56px !important;
}
.pc-px-56.data-v-487aa56b {
  padding-left: 56px !important;
  padding-right: 56px !important;
}
.pc-py-56.data-v-487aa56b {
  padding-top: 56px !important;
  padding-bottom: 56px !important;
}
.pc-m-56.data-v-487aa56b {
  margin: 56px !important;
}
.pc-mt-56.data-v-487aa56b {
  margin-top: 56px !important;
}
.pc-mr-56.data-v-487aa56b {
  margin-right: 56px !important;
}
.pc-mb-56.data-v-487aa56b {
  margin-bottom: 56px !important;
}
.pc-ml-56.data-v-487aa56b {
  margin-left: 56px !important;
}
.pc-mx-56.data-v-487aa56b {
  margin-left: 56px !important;
  margin-right: 56px !important;
}
.pc-my-56.data-v-487aa56b {
  margin-top: 56px !important;
  margin-bottom: 56px !important;
}
.pc-p-64.data-v-487aa56b {
  padding: 64px !important;
}
.pc-pt-64.data-v-487aa56b {
  padding-top: 64px !important;
}
.pc-pr-64.data-v-487aa56b {
  padding-right: 64px !important;
}
.pc-pb-64.data-v-487aa56b {
  padding-bottom: 64px !important;
}
.pc-pl-64.data-v-487aa56b {
  padding-left: 64px !important;
}
.pc-px-64.data-v-487aa56b {
  padding-left: 64px !important;
  padding-right: 64px !important;
}
.pc-py-64.data-v-487aa56b {
  padding-top: 64px !important;
  padding-bottom: 64px !important;
}
.pc-m-64.data-v-487aa56b {
  margin: 64px !important;
}
.pc-mt-64.data-v-487aa56b {
  margin-top: 64px !important;
}
.pc-mr-64.data-v-487aa56b {
  margin-right: 64px !important;
}
.pc-mb-64.data-v-487aa56b {
  margin-bottom: 64px !important;
}
.pc-ml-64.data-v-487aa56b {
  margin-left: 64px !important;
}
.pc-mx-64.data-v-487aa56b {
  margin-left: 64px !important;
  margin-right: 64px !important;
}
.pc-my-64.data-v-487aa56b {
  margin-top: 64px !important;
  margin-bottom: 64px !important;
}
.pc-hide-mobile.data-v-487aa56b {
  display: none !important;
}
@media (min-width: 768px) {
.pc-hide-tablet.data-v-487aa56b {
    display: none !important;
}
}
@media (min-width: 1024px) {
.pc-hide-desktop.data-v-487aa56b {
    display: none !important;
}
}
@media (min-width: 1440px) {
.pc-hide-large.data-v-487aa56b {
    display: none !important;
}
}
.pc-show-mobile.data-v-487aa56b {
  display: none !important;
  display: block !important;
}
.pc-show-tablet.data-v-487aa56b {
  display: none !important;
}
@media (min-width: 768px) {
.pc-show-tablet.data-v-487aa56b {
    display: block !important;
}
}
.pc-show-desktop.data-v-487aa56b {
  display: none !important;
}
@media (min-width: 1024px) {
.pc-show-desktop.data-v-487aa56b {
    display: block !important;
}
}
.pc-show-large.data-v-487aa56b {
  display: none !important;
}
@media (min-width: 1440px) {
.pc-show-large.data-v-487aa56b {
    display: block !important;
}
}
.pc-text-left.data-v-487aa56b {
  text-align: left !important;
}
.pc-text-center.data-v-487aa56b {
  text-align: center !important;
}
.pc-text-right.data-v-487aa56b {
  text-align: right !important;
}
.pc-flex.data-v-487aa56b {
  display: flex !important;
}
.pc-flex-column.data-v-487aa56b {
  flex-direction: column !important;
}
.pc-flex-row.data-v-487aa56b {
  flex-direction: row !important;
}
.pc-flex-wrap.data-v-487aa56b {
  flex-wrap: wrap !important;
}
.pc-flex-nowrap.data-v-487aa56b {
  flex-wrap: nowrap !important;
}
.pc-justify-start.data-v-487aa56b {
  justify-content: flex-start !important;
}
.pc-justify-center.data-v-487aa56b {
  justify-content: center !important;
}
.pc-justify-end.data-v-487aa56b {
  justify-content: flex-end !important;
}
.pc-justify-between.data-v-487aa56b {
  justify-content: space-between !important;
}
.pc-justify-around.data-v-487aa56b {
  justify-content: space-around !important;
}
.pc-align-start.data-v-487aa56b {
  align-items: flex-start !important;
}
.pc-align-center.data-v-487aa56b {
  align-items: center !important;
}
.pc-align-end.data-v-487aa56b {
  align-items: flex-end !important;
}
.pc-align-stretch.data-v-487aa56b {
  align-items: stretch !important;
}
.pc-only.data-v-487aa56b {
  display: none;
}
@media (min-width: 1024px) {
.pc-only.data-v-487aa56b {
    display: block;
}
}
.mobile-only.data-v-487aa56b {
  display: block;
}
@media (min-width: 1024px) {
.mobile-only.data-v-487aa56b {
    display: none;
}
}
.pc-page.data-v-487aa56b {
  min-height: 100vh;
  background: #f5f5f5;
}
@media (min-width: 1024px) {
.pc-page.data-v-487aa56b {
    padding: 20px;
}
}
.pc-page-content.data-v-487aa56b {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}
@media (min-width: 1024px) {
.pc-page-content.data-v-487aa56b {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
}
@media (min-width: 1024px) {
.pc-nav.data-v-487aa56b {
    padding: 0 32px;
    height: 60px;
    line-height: 60px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
}
@media (min-width: 1024px) {
.pc-button.data-v-487aa56b, .button.data-v-487aa56b {
    min-width: 120px;
    height: 40px;
    border-radius: 6px;
    font-size: 14px;
}
.pc-button.data-v-487aa56b:hover, .button.data-v-487aa56b:hover {
    opacity: 0.8;
    -webkit-transform: translateY(-1px);
            transform: translateY(-1px);
    transition: all 0.2s ease;
}
}
page.data-v-487aa56b {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}
.card.data-v-487aa56b {
  margin-bottom: 16px;
  padding: 16px;
}
page.data-v-487aa56b {
  height: 100%;
  background-color: #f5f7fa;
}
.material-list-ios.data-v-487aa56b {
  background: #f7f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.list-content.data-v-487aa56b {
  flex: 1;
  padding: 32rpx 0 120rpx 0;
}
.scroll-area.data-v-487aa56b {
  min-height: 60vh;
}
.material-card.data-v-487aa56b {
  background: #fff;
  border-radius: 24rpx;
  margin: 32rpx 32rpx 0 32rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  box-shadow: 0 4rpx 32rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.card-main.data-v-487aa56b {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.card-title-row.data-v-487aa56b {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.card-title.data-v-487aa56b {
  font-size: 36rpx;
  font-weight: 700;
  color: #222;
}
.card-tags.data-v-487aa56b {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12rpx;
}
.price-tag.data-v-487aa56b {
  font-size: 28rpx;
  padding: 8rpx 24rpx;
}
.version-tag.data-v-487aa56b {
  font-size: 22rpx;
  padding: 4rpx 16rpx;
}
.card-meta-row.data-v-487aa56b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24rpx;
}
.meta-item.data-v-487aa56b {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  font-size: 26rpx;
  color: #888;
}
.center-value.data-v-487aa56b {
  display: flex;
  align-items: center;
  height: 100%;
}
.meta-label.data-v-487aa56b {
  margin-left: 4rpx;
}
.meta-value.data-v-487aa56b {
  color: #39b54a;
  font-weight: 600;
  margin-left: 8rpx;
}
.meta-action.data-v-487aa56b {
  display: flex;
  align-items: center;
}
.detail-btn.data-v-487aa56b {
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  font-weight: 500;
  border-radius: 16rpx;
}
.float-action-container.data-v-487aa56b {
  position: fixed;
  bottom: 30rpx;
  right: 30rpx;
  z-index: 99;
}
.share-button.data-v-487aa56b {
  background: linear-gradient(135deg, #0081ff, #1cbbb4);
  border-radius: 50rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 129, 255, 0.3);
  padding: 16rpx 32rpx;
  display: flex;
  align-items: center;
}
.share-button text.data-v-487aa56b {
  color: #ffffff;
}
.share-button .cuIcon-add.data-v-487aa56b {
  font-size: 32rpx;
  margin-right: 8rpx;
}

