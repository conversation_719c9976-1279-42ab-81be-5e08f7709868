{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/safeCheck/safeCheck.vue?8ca4", "webpack:///D:/桌面/thinker/app/components/safeCheck/safeCheck.vue?60a3", "webpack:///D:/桌面/thinker/app/components/safeCheck/safeCheck.vue?27c0", "webpack:///D:/桌面/thinker/app/components/safeCheck/safeCheck.vue?94db", "uni-app:///components/safeCheck/safeCheck.vue"], "names": ["name", "data", "src", "code", "props", "sid", "type", "default", "scene", "title", "status", "confirmText", "confirmTextClass", "watch", "created", "methods", "inputTap", "confirmTap", "app", "mobile", "action", "setStatusTap", "refreshCodeSrcTap"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;;;AAGxD;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,2pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8BjrB;AAAA,eACA;EACAA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACAR;MACA;QACA;QACA;MACA;IACA;EACA;EACAS;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA;cAAA;gBAAA;gBAAA,OAGAA;kBACAf;kBACAK;kBACAW;gBACA;cAAA;gBACA;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;QACAD;MACA;MACA;IACA;IACAE;MACA;MACA;QACA,WACAJ;MACA;IAEA;EACA;AACA;AAAA,2B", "file": "components/safeCheck/safeCheck.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./safeCheck.vue?vue&type=template&id=2294037c&\"\nvar renderjs\nimport script from \"./safeCheck.vue?vue&type=script&lang=js&\"\nexport * from \"./safeCheck.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/safeCheck/safeCheck.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./safeCheck.vue?vue&type=template&id=2294037c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./safeCheck.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./safeCheck.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"cu-modal\" :class=\"status==true ? 'show':''\">\r\n\t\t\t<view class=\"cu-dialog\">\r\n\t\t\t\t<view class=\"cu-bar bg-white justify-end\">\r\n\t\t\t\t\t<view class=\"content\">{{title}}</view>\r\n\t\t\t\t\t<view class=\"action\" @tap=\"setStatusTap(false)\">\r\n\t\t\t\t\t\t<text class=\"cuIcon-close text-red\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"padding-xl border\">\r\n\t\t\t\t\t<view class=\"bg-img\" :style=\"'background-image: url('+src+');height:130rpx;'\"\r\n\t\t\t\t\t\t@tap=\"refreshCodeSrcTap\">\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"padding-xl\">\r\n\t\t\t\t\t<view class=\"cu-form-group\">\r\n\t\t\t\t\t\t<input placeholder=\"请输入图形验证码\" @input=\"inputTap\" :value=\"code\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"cu-bar bg-white\">\r\n\t\t\t\t\t<button class=\"action  margin flex-sub text-green\" @tap=\"confirmTap\">\r\n\t\t\t\t\t\t<text :class=\"confirmTextClass\"></text>{{confirmText}}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tname: \"safeCheck\",\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsrc: \"\",\r\n\t\t\t\tcode: \"\",\r\n\t\t\t};\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tsid: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tscene: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '安全验证'\r\n\t\t\t},\r\n\t\t\tstatus: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false,\r\n\t\t\t},\r\n\t\t\tconfirmText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tconfirmTextClass: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tsid(curVal, oldVal) {\r\n\t\t\t\tif (curVal.length == 11 && curVal != oldVal) {\r\n\t\t\t\t\tthis.sid = curVal;\r\n\t\t\t\t\tthis.refreshCodeSrcTap();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.code = '';\r\n\t\t\tthis.refreshCodeSrcTap();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinputTap(options) {\r\n\t\t\t\tthis.code = options.detail.value\r\n\t\t\t},\r\n\t\t\tasync confirmTap() {\r\n\t\t\t\tif (this.code == \"\") {\r\n\t\t\t\t\tapp.showToast('请输入图形验证码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tawait app.globalData.service.sendSmsCode({\r\n\t\t\t\t\tcode: this.code,\r\n\t\t\t\t\tscene: this.scene,\r\n\t\t\t\t\tmobile: this.sid,\r\n\t\t\t\t});\r\n\t\t\t\tthis.$emit('complete', {\r\n\t\t\t\t\taction: 1\r\n\t\t\t\t})\r\n\t\t\t\tthis.setStatusTap(false);\r\n\t\t\t},\r\n\t\t\tsetStatusTap(status) {\r\n\t\t\t\tthis.$emit('update:status', status)\r\n\t\t\t\tthis.$emit('complete', {\r\n\t\t\t\t\taction: 2\r\n\t\t\t\t})\r\n\t\t\t\tthis.code = '';\r\n\t\t\t},\r\n\t\t\trefreshCodeSrcTap() {\r\n\t\t\t\tlet sid = this.sid;\r\n\t\t\t\tif (sid != '') {\r\n\t\t\t\t\tthis.src =\r\n\t\t\t\t\t\tapp.globalData.config.apiUrl + 'get_captcha_img?key=' + sid + '&rand=' + Math.random();\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n"], "sourceRoot": ""}