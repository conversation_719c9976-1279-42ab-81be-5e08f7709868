@charset "UTF-8";
/**
 * uni-app全局样式 - PC端适配版本
 */
/**
 * PC端适配样式
 * 实现小程序在PC端的缩放适配
 */
@media (min-width: 1024px) {
.pc-app-container.data-v-5ca1ce02 {
    width: 420px;
    margin: 0 auto;
    -webkit-transform-origin: top center;
            transform-origin: top center;
    -webkit-transform: scale(0.75);
            transform: scale(0.75);
    background: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    min-height: 100vh;
}
}
@media (max-width: 1023px) {
.pc-app-container.data-v-5ca1ce02 {
    width: 100%;
    margin: 0;
    -webkit-transform: none;
            transform: none;
    box-shadow: none;
    border-radius: 0;
}
}
@media (min-width: 1024px) {
.pc-page-background.data-v-5ca1ce02 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 40px 20px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}
}
@media (max-width: 1023px) {
.pc-page-background.data-v-5ca1ce02 {
    background: #f5f5f5;
    min-height: 100vh;
    padding: 0;
}
}
@media (min-width: 1024px) {
.pc-scale-container.data-v-5ca1ce02 {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.pc-scale-container .pc-app-wrapper.data-v-5ca1ce02 {
    width: 375px;
    height: 667px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    -webkit-transform: scale(0.75);
            transform: scale(0.75);
    -webkit-transform-origin: center;
            transform-origin: center;
    position: relative;
}
}
@media (max-width: 1023px) {
.pc-scale-container.data-v-5ca1ce02 {
    width: 100%;
    height: 100vh;
}
.pc-scale-container .pc-app-wrapper.data-v-5ca1ce02 {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
    -webkit-transform: none;
            transform: none;
}
}
@media (max-width: 1023px) {
.pc-only.data-v-5ca1ce02 {
    display: none !important;
}
}
@media (min-width: 1024px) {
.mobile-only.data-v-5ca1ce02 {
    display: none !important;
}
}
@media (min-width: 1024px) {
page.data-v-5ca1ce02, page.data-v-5ca1ce02 {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}
.uni-page-head.data-v-5ca1ce02 {
    display: none !important;
}
.uni-page-wrapper.data-v-5ca1ce02 {
    height: 100vh !important;
}
}
page.data-v-5ca1ce02 {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}
@media (min-width: 1024px) {
page.data-v-5ca1ce02 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
}
.uni-page-wrapper.data-v-5ca1ce02 {
    width: 375px !important;
    height: 667px !important;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
    -webkit-transform-origin: center;
            transform-origin: center;
    position: relative;
}
.uni-tabbar.data-v-5ca1ce02 {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
}
}
.pc-demo.data-v-5ca1ce02 {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 20px;
}
.header.data-v-5ca1ce02 {
  text-align: center;
  margin-bottom: 30px;
}
.header .title.data-v-5ca1ce02 {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}
.header .subtitle.data-v-5ca1ce02 {
  display: block;
  font-size: 14px;
  color: #666;
}
.status-card.data-v-5ca1ce02 {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
.status-card .status-item.data-v-5ca1ce02 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}
.status-card .status-item.data-v-5ca1ce02:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}
.status-card .status-label.data-v-5ca1ce02 {
  font-size: 14px;
  color: #666;
}
.status-card .status-value.data-v-5ca1ce02 {
  font-size: 14px;
  font-weight: bold;
  color: #333;
}
.status-card .status-value.pc-device.data-v-5ca1ce02 {
  color: #52c41a;
}
.demo-section.data-v-5ca1ce02, .interaction-section.data-v-5ca1ce02 {
  margin-bottom: 30px;
}
.demo-section .section-title.data-v-5ca1ce02, .interaction-section .section-title.data-v-5ca1ce02 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}
.card-list.data-v-5ca1ce02 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}
.demo-card.data-v-5ca1ce02 {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}
.demo-card .card-icon.data-v-5ca1ce02 {
  font-size: 24px;
  margin-right: 12px;
}
.demo-card .card-content.data-v-5ca1ce02 {
  flex: 1;
}
.demo-card .card-content .card-title.data-v-5ca1ce02 {
  display: block;
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}
.demo-card .card-content .card-desc.data-v-5ca1ce02 {
  display: block;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}
.button-grid.data-v-5ca1ce02 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}
.demo-btn.data-v-5ca1ce02 {
  padding: 12px 16px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}
.demo-btn.primary.data-v-5ca1ce02 {
  background: #1890ff;
  color: white;
}
.demo-btn.primary.data-v-5ca1ce02:active {
  background: #096dd9;
}
.demo-btn.secondary.data-v-5ca1ce02 {
  background: #f5f5f5;
  color: #666;
}
.demo-btn.secondary.data-v-5ca1ce02:active {
  background: #e6e6e6;
}
.demo-btn.success.data-v-5ca1ce02 {
  background: #52c41a;
  color: white;
}
.demo-btn.success.data-v-5ca1ce02:active {
  background: #389e0d;
}
.demo-btn.warning.data-v-5ca1ce02 {
  background: #faad14;
  color: white;
}
.demo-btn.warning.data-v-5ca1ce02:active {
  background: #d48806;
}
.footer.data-v-5ca1ce02 {
  text-align: center;
  margin-top: 40px;
}
.footer .footer-text.data-v-5ca1ce02 {
  font-size: 12px;
  color: #999;
}

