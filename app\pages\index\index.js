let that = null,
	app = getApp(),
	config = app.globalData.config,
	cache = config.storage,
	helper = app.globalData.helper;
export default {
	data() {
		return {
			images: [],
			appIsAudit: false,
			showWxGroup: false,
			showWxGroupText: '',
			appPlatform: app.globalData.appPlatform,
			showIndexShareName: '',
			articleList: [],
			currentCity: {},
			currentExam: {},
			currentProfession: {},
			menuList: [],
			examTime: {},
			isLoading: true,
			showPCStatus: false // PC端状态显示控制
		};
	},
	onLoad(options) {
		app.globalData.showShareMenu();
		that = this;

		// 初始化PC端适配
		that.initPCAdaptation();
	},
	onShow(options) {
		that.getHomeData();
		that.initIndexExamData();
	},
	onUnload() {
		// 移除窗口变化监听
		if (typeof wx !== 'undefined' && wx.offWindowResize) {
			wx.offWindowResize();
		}
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		/**
		 * 初始化PC端适配
		 */
		initPCAdaptation() {
			// 获取多端适配器
			const adapter = app.globalData.multiPlatformAdapter;

			if (adapter.isPC()) {
				console.log('首页 - PC端适配初始化');

				// 启用PC状态显示
				that.showPCStatus = true;

				// 获取PC端窗口状态
				const windowState = adapter.getWindowState();
				console.log('首页 - PC端窗口状态:', windowState);

				// 监听窗口变化
				uni.$on('windowResize', (data) => {
					console.log('首页 - 窗口变化:', data);
					that.handleWindowResize(data);
				});

				// 监听PC样式应用
				uni.$on('applyPCStyles', (data) => {
					console.log('首页 - 应用PC样式:', data);
					that.applyPCStyles(data);
				});

				// 显示PC端适配提示
				uni.showToast({
					title: 'PC端适配已启用',
					icon: 'success',
					duration: 2000
				});
			}
		},

		/**
		 * 处理窗口变化
		 */
		handleWindowResize(data) {
			const { size, adapter } = data;
			console.log('首页窗口变化处理:', {
				newSize: size,
				windowMode: adapter.device.screenType
			});

			// 可以在这里根据窗口大小调整布局
			// 例如：调整网格列数、字体大小等
		},

		/**
		 * 应用PC端样式
		 */
		applyPCStyles(data) {
			console.log('首页应用PC样式:', data);
			// 可以在这里动态调整样式
		},

		/**
		 * 通用导航方法
		 * @param {String} url 完整的导航地址
		 */
		goTo(url) {
			uni.navigateTo({url});
		},
		
		checkAppIsAudit() {
			that.appIsAudit = app.globalData.checkAppIsAudit();
		},
		initIndexExamData() {
			that.currentCity = cache.getCurrentCityData();
			that.currentExam = cache.getCurrentExamData();
			that.currentProfession = cache.getCurrentProfessionData();
			that.changeExamInfo();
		},
		async changeExamInfo() {
			let exam_id = helper.variableDefalut(that.currentExam.id, 0),
				region_id = helper.variableDefalut(that.currentCity.id, 0),
				profession_id = helper.variableDefalut(that.currentProfession.id, 0);
			if (region_id == 0) {
				that.goTo('city/city?first_visit=1');
				return;
			}
			if (profession_id && app.globalData.isLogin) {
				let user = cache.getUserInfoData(),
					user_profession_id = helper
					.variableDefalut(user.profession_id, 0);
				if (user_profession_id != profession_id) {
					await app.globalData.service.userUpdate({
						exam_id: exam_id,
						region_id: region_id,
						profession_id: profession_id
					});
					user.profession_id = profession_id;
					cache.setUserInfoData(user);
				}
			}
		},
		
		/**
		 * 获取首页数据
		 */
		getHomeData() {
			that.isLoading = true;
			// 优先加载缓存
			let time = app.globalData.getTimestamp();
			let cacheHomeData = wx.getStorageSync(cache.homeKey)
			console.log(cacheHomeData, time);
			if (cacheHomeData && cacheHomeData.expire >= time) {
				that.images = cacheHomeData.swiper;
				that.menuList = cacheHomeData.menuList
				that.articleList = cacheHomeData.articleList;
				that.showWxGroup = cacheHomeData.appConfig.showWxGroup
				that.showWxGroupText = cacheHomeData.appConfig.showWxGroupText
				that.showIndexShareName = cacheHomeData.appConfig.showIndexShareName
				// 从缓存中获取考试时间数据
				if (cacheHomeData.examTime) {
					that.examTime = cacheHomeData.examTime;
				}
				that.isLoading = false;
				return;
			}

			// 加载接口数据
			let cacheTime = 0;
			app.globalData.server
				.getRequest('home', {})
				.then((res) => {
					that.images = res.data.swiper;
					that.menuList = res.data.menuList;
					that.articleList = res.data.articleList;
					that.showWxGroup = res.data.appConfig.showWxGroup;
					that.showWxGroupText = res.data.appConfig.showWxGroupText;
					that.showIndexShareName = res.data.appConfig.showIndexShareName;
					
					// 获取考试时间数据
					if (res.data.examTime) {
						that.examTime = res.data.examTime;
					}
					
					res.data.expire = time + cacheTime;
					cache.setHomeData(res.data);
					that.checkAppIsAudit();
					that.isLoading = false;
				})
				.catch((e) => {
					console.log(e)
					app.showToast('获取首页数据失败');
					that.isLoading = false;
				});
		}
	}
};