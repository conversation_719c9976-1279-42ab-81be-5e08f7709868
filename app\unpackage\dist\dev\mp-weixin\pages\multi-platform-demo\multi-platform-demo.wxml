<view class="multi-platform-demo data-v-3bff8c4c"><view class="info-card data-v-3bff8c4c"><view class="card-title data-v-3bff8c4c">多端适配信息</view><view class="info-list data-v-3bff8c4c"><view class="info-item data-v-3bff8c4c"><text class="label data-v-3bff8c4c">当前平台：</text><text class="value data-v-3bff8c4c">{{platformName}}</text></view><view class="info-item data-v-3bff8c4c"><text class="label data-v-3bff8c4c">设备类型：</text><text class="value data-v-3bff8c4c">{{deviceType}}</text></view><view class="info-item data-v-3bff8c4c"><text class="label data-v-3bff8c4c">屏幕类型：</text><text class="value data-v-3bff8c4c">{{screenType}}</text></view><view class="info-item data-v-3bff8c4c"><text class="label data-v-3bff8c4c">是否PC端：</text><text class="{{['value','data-v-3bff8c4c',(isPC)?'pc-indicator':'']}}">{{isPC?'是':'否'}}</text></view><block wx:if="{{isPC}}"><view class="info-item data-v-3bff8c4c"><text class="label data-v-3bff8c4c">PC缩放比例：</text><text class="value data-v-3bff8c4c">{{pcScaleRatio}}</text></view></block></view></view><view class="demo-card data-v-3bff8c4c"><view class="card-title data-v-3bff8c4c">响应式演示</view><view class="demo-grid data-v-3bff8c4c"><block wx:for="{{demoItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="demo-item data-v-3bff8c4c"><view class="item-icon data-v-3bff8c4c">{{item.icon}}</view><text class="item-title data-v-3bff8c4c">{{item.title}}</text></view></block></view></view><view class="demo-card data-v-3bff8c4c"><view class="card-title data-v-3bff8c4c">平台特定显示</view><view class="platform-specific data-v-3bff8c4c"><view class="mobile-only demo-alert mobile data-v-3bff8c4c">移动端专用</view><view class="pc-only demo-alert pc data-v-3bff8c4c">PC端专用</view><view class="demo-alert default data-v-3bff8c4c">通用内容</view></view></view><view class="demo-card data-v-3bff8c4c"><view class="card-title data-v-3bff8c4c">操作测试</view><view class="button-group data-v-3bff8c4c"><button data-event-opts="{{[['tap',[['refreshInfo',['$event']]]]]}}" class="demo-btn primary data-v-3bff8c4c" bindtap="__e">刷新信息</button><button data-event-opts="{{[['tap',[['testToast',['$event']]]]]}}" class="demo-btn secondary data-v-3bff8c4c" bindtap="__e">测试提示</button><block wx:if="{{isPC}}"><button data-event-opts="{{[['tap',[['adjustScale',['$event']]]]]}}" class="demo-btn data-v-3bff8c4c" bindtap="__e">调整缩放</button></block></view></view></view>