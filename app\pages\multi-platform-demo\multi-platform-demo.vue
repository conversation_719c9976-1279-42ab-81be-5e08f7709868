<template>
  <view class="multi-platform-demo">
    <!-- 基础信息展示 -->
    <view class="info-card">
      <view class="card-title">多端适配信息</view>
      
      <view class="info-list">
        <view class="info-item">
          <text class="label">当前平台：</text>
          <text class="value">{{ platformName }}</text>
        </view>
        
        <view class="info-item">
          <text class="label">设备类型：</text>
          <text class="value">{{ deviceType }}</text>
        </view>
        
        <view class="info-item">
          <text class="label">屏幕类型：</text>
          <text class="value">{{ screenType }}</text>
        </view>
        
        <view class="info-item">
          <text class="label">是否PC端：</text>
          <text class="value" :class="{ 'pc-indicator': isPC }">{{ isPC ? '是' : '否' }}</text>
        </view>
        
        <view class="info-item" v-if="isPC">
          <text class="label">PC缩放比例：</text>
          <text class="value">{{ pcScaleRatio }}</text>
        </view>
      </view>
    </view>

    <!-- 响应式演示 -->
    <view class="demo-card">
      <view class="card-title">响应式演示</view>
      
      <view class="demo-grid">
        <view class="demo-item" v-for="(item, index) in demoItems" :key="index">
          <view class="item-icon">{{ item.icon }}</view>
          <text class="item-title">{{ item.title }}</text>
        </view>
      </view>
    </view>

    <!-- 平台特定显示 -->
    <view class="demo-card">
      <view class="card-title">平台特定显示</view>
      
      <view class="platform-specific">
        <view class="mobile-only demo-alert mobile">移动端专用</view>
        <view class="pc-only demo-alert pc">PC端专用</view>
        <view class="demo-alert default">通用内容</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="demo-card">
      <view class="card-title">操作测试</view>
      
      <view class="button-group">
        <button class="demo-btn primary" @click="refreshInfo">刷新信息</button>
        <button class="demo-btn secondary" @click="testToast">测试提示</button>
        <button class="demo-btn" v-if="isPC" @click="adjustScale">调整缩放</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MultiPlatformDemo',
  data() {
    return {
      platformName: '未知',
      deviceType: '未知',
      screenType: '未知',
      isPC: false,
      pcScaleRatio: '0.8',
      
      demoItems: [
        { icon: '📱', title: '移动端' },
        { icon: '📟', title: '平板端' },
        { icon: '💻', title: '桌面端' },
        { icon: '🖥️', title: '大屏端' }
      ]
    }
  },
  
  mounted() {
    this.updateInfo()
  },
  
  methods: {
    updateInfo() {
      try {
        const app = getApp()
        if (app.globalData && app.globalData.multiPlatformAdapter) {
          const adapter = app.globalData.multiPlatformAdapter
          const adapterInfo = adapter.getAdapterInfo()
          
          this.platformName = this.getPlatformName(adapterInfo.platform.current)
          this.deviceType = this.getDeviceTypeName(adapterInfo.device)
          this.screenType = this.getScreenTypeName(adapterInfo.device.screenType)
          this.isPC = adapterInfo.platform.isPC
          
          if (adapterInfo.config && adapterInfo.config.pcScale) {
            this.pcScaleRatio = adapterInfo.config.pcScale.ratio || '0.8'
          }
        }
      } catch (error) {
        console.error('获取适配信息失败:', error)
      }
    },
    
    getPlatformName(platform) {
      const names = {
        'h5': 'H5网页',
        'app': 'App应用',
        'mp-weixin': '微信小程序',
        'mp-alipay': '支付宝小程序',
        'mp-baidu': '百度小程序'
      }
      return names[platform] || platform || '未知'
    },
    
    getDeviceTypeName(device) {
      if (device.isDesktop) return '桌面设备'
      if (device.isTablet) return '平板设备'
      if (device.isMobile) return '移动设备'
      return '未知设备'
    },
    
    getScreenTypeName(screenType) {
      const names = {
        'mobile': '移动端',
        'tablet': '平板端',
        'desktop': '桌面端',
        'large': '大屏端'
      }
      return names[screenType] || screenType || '未知'
    },
    
    refreshInfo() {
      this.updateInfo()
      uni.showToast({
        title: '信息已刷新',
        icon: 'success'
      })
    },
    
    testToast() {
      uni.showToast({
        title: '测试成功！',
        icon: 'success'
      })
    },
    
    adjustScale() {
      try {
        const app = getApp()
        if (app.globalData && app.globalData.multiPlatformAdapter) {
          const currentRatio = parseFloat(this.pcScaleRatio)
          const newRatio = currentRatio >= 1.0 ? 0.6 : currentRatio + 0.1
          
          app.globalData.multiPlatformAdapter.setPCScaleRatio(newRatio)
          this.pcScaleRatio = newRatio.toFixed(1)
          
          uni.showToast({
            title: `缩放: ${this.pcScaleRatio}`,
            icon: 'success'
          })
        }
      } catch (error) {
        console.error('调整缩放失败:', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.multi-platform-demo {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 16px;
}

.info-card,
.demo-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.info-list {
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
  }
}

.label {
  font-size: 14px;
  color: #666;
}

.value {
  font-size: 14px;
  color: #333;
  
  &.pc-indicator {
    color: #52c41a;
    font-weight: bold;
  }
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.demo-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  text-align: center;
  border: 2px dashed #dee2e6;
  
  .item-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
  
  .item-title {
    display: block;
    font-size: 14px;
    color: #666;
  }
}

.platform-specific {
  .demo-alert {
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    text-align: center;
    font-weight: 500;
    
    &.mobile {
      background: #e3f2fd;
      color: #1976d2;
    }
    
    &.pc {
      background: #e8f5e8;
      color: #388e3c;
    }
    
    &.default {
      background: #f5f5f5;
      color: #666;
    }
  }
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.demo-btn {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  
  &.primary {
    background: #1890ff;
    color: white;
  }
  
  &.secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #d9d9d9;
  }
  
  &:not(.primary):not(.secondary) {
    background: #52c41a;
    color: white;
  }
}

// 响应式样式
@media (min-width: 768px) {
  .demo-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

// PC/移动端显示控制
.mobile-only {
  display: block;
}

.pc-only {
  display: none;
}

@media (min-width: 1024px) {
  .mobile-only {
    display: none;
  }
  
  .pc-only {
    display: block;
  }
  
  .demo-item:hover {
    border-color: #1890ff;
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
}
</style>
