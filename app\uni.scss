/**
 * uni-app全局样式 - 多端适配版本
 */

// 导入多端适配样式
@import '@/common/css/pc-responsive.scss';

// ==================== 全局基础样式 ====================

* {
  box-sizing: border-box;
}

page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
}

// ==================== 全局组件样式 ====================

// 容器样式
.container {
  @extend .responsive-container;
}

// 卡片样式
.card {
  @extend .responsive-card;
}

// 按钮样式
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &.primary {
    background: #1890ff;
    color: white;

    &:hover {
      background: #40a9ff;
    }

    &:active {
      background: #096dd9;
    }
  }

  &.secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #d9d9d9;

    &:hover {
      background: #fafafa;
      border-color: #40a9ff;
    }
  }
}

// ==================== 平台特定样式 ====================

// H5平台样式
.platform-h5 {
  // H5特定全局样式
}

// App平台样式
.platform-app {
  // App特定全局样式
}

// 微信小程序平台样式
.platform-mp-weixin {
  // 微信小程序特定全局样式
}

// ==================== PC端适配样式 ====================

// PC端全局适配
@include pc-only {
  page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
  }

  // 应用主容器
  .uni-page-wrapper {
    width: 375px !important;
    height: 667px !important;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: scale(0.8);
    transform-origin: center;
    position: relative;
    transition: transform 0.3s ease;
  }

  // 标签栏适配
  .uni-tabbar {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    border-radius: 0 0 12px 12px;
  }

  // 页面头部隐藏
  .uni-page-head {
    display: none !important;
  }
}

// ==================== 响应式工具类 ====================

// 显示/隐藏
.show-mobile { @extend .show-mobile; }
.show-tablet { @extend .show-tablet; }
.show-desktop { @extend .show-desktop; }
.show-large { @extend .show-large; }

.hide-mobile { @extend .hide-mobile; }
.hide-tablet { @extend .hide-tablet; }
.hide-desktop { @extend .hide-desktop; }
.hide-large { @extend .hide-large; }

.pc-only { @extend .pc-only; }
.mobile-only { @extend .mobile-only; }

// 布局
.flex { @extend .flex; }
.flex-column { @extend .flex-column; }
.flex-row { @extend .flex-row; }
.justify-center { @extend .justify-center; }
.align-center { @extend .align-center; }

// 文本对齐
.text-left { @extend .text-left; }
.text-center { @extend .text-center; }
.text-right { @extend .text-right; }

// ==================== 动画和过渡 ====================

// 全局过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(100%);
}

// ==================== 性能优化 ====================

// 硬件加速
.gpu-accelerated {
  @extend .gpu-accelerated;
}

// 滚动优化
.smooth-scroll {
  @extend .smooth-scroll;
}