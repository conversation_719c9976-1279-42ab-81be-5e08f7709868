/**
 * uni-app全局样式 - PC端适配版本
 */

// 导入PC端适配样式
@import '@/common/css/pc-responsive.scss';

// 全局基础样式
* {
  box-sizing: border-box;
}

page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

// PC端适配：在PC端将整个应用缩放到合适大小
@media (min-width: 1024px) {
  page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    padding: 20px;
  }

  // 应用主容器
  .uni-page-wrapper {
    width: 375px !important;
    height: 667px !important;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    transform: scale(0.8);
    transform-origin: center;
    position: relative;
  }

  // 隐藏PC端不需要的元素
  .uni-tabbar {
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
  }
}