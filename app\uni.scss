/**
 * uni-app全局样式
 */

// 导入PC端响应式样式
@import '@/common/css/pc-responsive.scss';

// 全局基础样式
* {
  box-sizing: border-box;
}

page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

// 全局容器样式
.container {
  @extend .pc-container;
}

// 全局卡片样式
.card {
  @extend .pc-card;
  margin-bottom: 16px;
  padding: 16px;
}

// 全局按钮样式
.button {
  @extend .pc-button;
}