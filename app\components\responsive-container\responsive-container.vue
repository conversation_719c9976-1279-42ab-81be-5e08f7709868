<template>
  <view class="responsive-container" :class="containerClasses" :style="containerStyles">
    <slot></slot>
  </view>
</template>

<script>
import pcAdapter from '@/common/js/pc-adapter.js'

export default {
  name: 'ResponsiveContainer',
  props: {
    // 容器最大宽度配置
    maxWidth: {
      type: [String, Number, Object],
      default: () => ({
        mobile: '100%',
        tablet: '750px',
        desktop: '1000px',
        large: '1200px'
      })
    },
    // 内边距配置
    padding: {
      type: [String, Number, Object],
      default: () => ({
        mobile: '16px',
        tablet: '24px',
        desktop: '32px',
        large: '40px'
      })
    },
    // 是否居中
    center: {
      type: Boolean,
      default: true
    },
    // 背景色
    background: {
      type: String,
      default: null
    },
    // 是否为流体容器（不限制最大宽度）
    fluid: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentBreakpoint: 'mobile',
      isPC: false
    }
  },
  computed: {
    containerClasses() {
      const classes = ['responsive-container']
      
      classes.push(`responsive-container--${this.currentBreakpoint}`)
      
      if (this.center) {
        classes.push('responsive-container--center')
      }
      
      if (this.fluid) {
        classes.push('responsive-container--fluid')
      }
      
      if (this.isPC) {
        classes.push('responsive-container--pc')
      }
      
      return classes
    },
    
    containerStyles() {
      const styles = {}
      
      // 最大宽度
      if (!this.fluid) {
        const maxWidth = pcAdapter.getResponsiveValue(this.maxWidth)
        if (maxWidth) {
          styles.maxWidth = typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth
        }
      }
      
      // 内边距
      const padding = pcAdapter.getResponsiveValue(this.padding)
      if (padding) {
        styles.padding = typeof padding === 'number' ? `${padding}px` : padding
      }
      
      // 背景色
      if (this.background) {
        styles.backgroundColor = this.background
      }
      
      return styles
    }
  },
  mounted() {
    this.updateDeviceInfo()
    this.setupResizeListener()
  },
  beforeDestroy() {
    this.removeResizeListener()
  },
  methods: {
    updateDeviceInfo() {
      const deviceInfo = pcAdapter.getDeviceInfo()
      this.currentBreakpoint = deviceInfo.breakpoint
      this.isPC = deviceInfo.isPC
    },
    
    setupResizeListener() {
      this.resizeHandler = (data) => {
        this.currentBreakpoint = data.breakpoint
        this.isPC = data.isPC
      }
      pcAdapter.onWindowResize(this.resizeHandler)
    },
    
    removeResizeListener() {
      if (this.resizeHandler) {
        pcAdapter.offWindowResize(this.resizeHandler)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.responsive-container {
  width: 100%;
  box-sizing: border-box;
  
  &--center {
    margin-left: auto;
    margin-right: auto;
  }
  
  &--fluid {
    max-width: none !important;
  }
  
  &--pc {
    min-height: 100vh;
    
    // PC端特殊样式
    &.responsive-container--desktop,
    &.responsive-container--large {
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      margin-top: 20px;
      margin-bottom: 20px;
    }
  }
}

// 响应式样式
.responsive-container--mobile {
  padding: 16px;
}

.responsive-container--tablet {
  padding: 24px;
}

.responsive-container--desktop {
  padding: 32px;
}

.responsive-container--large {
  padding: 40px;
}
</style>
