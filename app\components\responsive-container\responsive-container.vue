<template>
  <view class="responsive-container" :class="containerClasses" :style="containerStyles">
    <slot></slot>
  </view>
</template>

<script>
import multiPlatformAdapter from '@/common/js/pc-adapter.js'

export default {
  name: 'ResponsiveContainer',
  props: {
    // 容器最大宽度配置
    maxWidth: {
      type: [String, Number, Object],
      default: () => ({
        mobile: '100%',
        tablet: '750px',
        desktop: '1000px',
        large: '1200px'
      })
    },

    // 内边距配置
    padding: {
      type: [String, Number, Object],
      default: () => ({
        mobile: '16px',
        tablet: '24px',
        desktop: '32px',
        large: '40px'
      })
    },

    // 外边距配置
    margin: {
      type: [String, Number, Object],
      default: null
    },

    // 是否居中
    center: {
      type: Boolean,
      default: true
    },

    // 背景配置
    background: {
      type: [String, Object],
      default: null
    },

    // 是否为流体容器（不限制最大宽度）
    fluid: {
      type: Boolean,
      default: false
    },

    // 是否启用PC端优化
    pcOptimized: {
      type: Boolean,
      default: true
    },

    // 是否启用阴影
    shadow: {
      type: [Boolean, String, Object],
      default: false
    },

    // 边框圆角
    radius: {
      type: [String, Number, Object],
      default: null
    }
  },
  data() {
    return {
      adapterInfo: {
        platform: {},
        device: {},
        screenType: 'mobile'
      }
    }
  },

  computed: {
    containerClasses() {
      const classes = ['responsive-container']

      // 屏幕类型类
      classes.push(`responsive-container--${this.adapterInfo.device.screenType}`)

      // 平台类
      if (this.adapterInfo.platform.current) {
        classes.push(`responsive-container--platform-${this.adapterInfo.platform.current}`)
      }

      // 功能类
      if (this.center) {
        classes.push('responsive-container--center')
      }

      if (this.fluid) {
        classes.push('responsive-container--fluid')
      }

      if (this.adapterInfo.platform.isPC) {
        classes.push('responsive-container--pc')
      }

      if (this.pcOptimized && this.adapterInfo.platform.isPC) {
        classes.push('responsive-container--pc-optimized')
      }

      // 阴影类
      if (this.shadow) {
        if (typeof this.shadow === 'boolean') {
          classes.push('responsive-container--shadow')
        } else {
          const shadowValue = this.getResponsiveValue(this.shadow)
          if (shadowValue) {
            classes.push(`responsive-container--shadow-${shadowValue}`)
          }
        }
      }

      return classes
    },
    
    containerStyles() {
      const styles = {}

      // 最大宽度
      if (!this.fluid) {
        const maxWidth = this.getResponsiveValue(this.maxWidth)
        if (maxWidth) {
          styles.maxWidth = this.formatValue(maxWidth)
        }
      }

      // 内边距
      const padding = this.getResponsiveValue(this.padding)
      if (padding) {
        styles.padding = this.formatValue(padding)
      }

      // 外边距
      const margin = this.getResponsiveValue(this.margin)
      if (margin) {
        styles.margin = this.formatValue(margin)
      }

      // 背景
      const background = this.getResponsiveValue(this.background)
      if (background) {
        if (typeof background === 'string') {
          styles.backgroundColor = background
        } else if (typeof background === 'object') {
          Object.assign(styles, background)
        }
      }

      // 边框圆角
      const radius = this.getResponsiveValue(this.radius)
      if (radius) {
        styles.borderRadius = this.formatValue(radius)
      }

      return styles
    }
  },

  mounted() {
    this.updateAdapterInfo()
    this.setupEventListeners()
  },

  beforeDestroy() {
    this.removeEventListeners()
  },

  methods: {
    updateAdapterInfo() {
      this.adapterInfo = multiPlatformAdapter.getAdapterInfo()
    },

    setupEventListeners() {
      this.resizeHandler = () => {
        this.updateAdapterInfo()
      }

      multiPlatformAdapter.on('resize', this.resizeHandler)
    },

    removeEventListeners() {
      if (this.resizeHandler) {
        multiPlatformAdapter.off('resize', this.resizeHandler)
      }
    },

    getResponsiveValue(value) {
      return multiPlatformAdapter.getResponsiveValue(value)
    },

    formatValue(value) {
      if (typeof value === 'number') {
        return `${value}px`
      }
      return value
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/common/css/pc-responsive.scss';

.responsive-container {
  width: 100%;
  box-sizing: border-box;
  transition: all 0.3s ease;

  &--center {
    margin-left: auto;
    margin-right: auto;
  }

  &--fluid {
    max-width: none !important;
  }

  // 平台特定样式
  &--platform-h5 {
    // H5特定样式
  }

  &--platform-app {
    // App特定样式
  }

  &--platform-mp-weixin {
    // 微信小程序特定样式
  }

  // PC端样式
  &--pc {
    @include pc-only {
      min-height: auto;
    }
  }

  &--pc-optimized {
    @include pc-only {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      }
    }
  }

  // 阴影样式
  &--shadow {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &--shadow-sm {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  &--shadow-md {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &--shadow-lg {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  }

  &--shadow-xl {
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
  }
}

// 响应式样式
.responsive-container--mobile {
  @include respond-to(mobile) {
    padding: 16px;
  }
}

.responsive-container--tablet {
  @include respond-to(tablet) {
    padding: 24px;
  }
}

.responsive-container--desktop {
  @include respond-to(desktop) {
    padding: 32px;
  }
}

.responsive-container--large {
  @include respond-to(large) {
    padding: 40px;
  }
}
</style>
