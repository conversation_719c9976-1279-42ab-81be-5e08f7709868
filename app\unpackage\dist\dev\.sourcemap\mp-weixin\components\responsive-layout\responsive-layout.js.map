{"version": 3, "sources": ["webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?1343", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?0c78", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?c618", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?36a8", "uni-app:///components/responsive-layout/responsive-layout.vue", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?efc1", "webpack:///D:/桌面/thinker/app/components/responsive-layout/responsive-layout.vue?2dc4"], "names": ["name", "props", "type", "default", "validator", "responsive", "max<PERSON><PERSON><PERSON>", "padding", "margin", "cols", "mobile", "tablet", "desktop", "large", "gap", "align", "justify", "hidden", "visible", "pcOptimized", "data", "adapterInfo", "platform", "device", "screenType", "computed", "layoutClasses", "classes", "layoutStyles", "styles", "Object", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "updateAdapterInfo", "setupEventListeners", "multiPlatformAdapter", "removeEventListeners", "getResponsiveValue", "formatValue", "shouldHide"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACsC;;;AAGtG;AAC8K;AAC9K,gBAAgB,qLAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqqB,CAAgB,mqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACOzrB;;;;;;;gBAEA;EACAA;EACAC;IACA;IACAC;MACAA;MACAC;MACAC;QAAA;MAAA;IACA;IAEA;IACAC;MACAH;MACAC;QAAA;MAAA;IACA;IAEA;IACAG;MACAJ;MACAC;IACA;IAEA;IACAI;MACAL;MACAC;IACA;IAEA;IACAK;MACAN;MACAC;IACA;IAEA;IACAM;MACAP;MACAC;QAAA;UACAO;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAC;MACAZ;MACAC;QAAA;UACAO;UACAC;UACAC;UACAC;QACA;MAAA;IACA;IAEA;IACAE;MACAb;MACAC;IACA;IAEA;IACAa;MACAd;MACAC;IACA;IAEA;IACAc;MACAf;MACAC;IACA;IAEAe;MACAhB;MACAC;IACA;IAEA;IACAgB;MACAjB;MACAC;IACA;EACA;EACAiB;IACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACAC;MACA;;MAEA;MACAC;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MAEA;QACAA;MACA;;MAEA;MACA;MACA;QACAA;MACA;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MAEA;IACA;IAEAC;MACA;;MAEA;MACA;MACA;QACAC;MACA;;MAEA;MACA;MACA;QACAA;MACA;;MAEA;MACA;MACA;QACAA;MACA;;MAEA;MACA;MACA;QACAA;MACA;;MAEA;MACA;QACA;QACA;UACAA;QACA;MACA;;MAEA;MACA;QACA;QACA;UACAC;QACA;MACA;MAEA;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IAEAC;MAAA;MACA;QACA;MACA;MAEAC;IACA;IAEAC;MACA;QACAD;MACA;IACA;IAEAE;MACA;IACA;IAEAC;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MAEA;QACA;QACA;UACA;QACA;MACA;MAEA;QACA;QACA;UACA;QACA;MACA;MAEA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC/PA;AAAA;AAAA;AAAA;AAAoxC,CAAgB,0sCAAG,EAAC,C;;;;;;;;;;;ACAxyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/responsive-layout/responsive-layout.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./responsive-layout.vue?vue&type=template&id=46ec3b4c&scoped=true&\"\nvar renderjs\nimport script from \"./responsive-layout.vue?vue&type=script&lang=js&\"\nexport * from \"./responsive-layout.vue?vue&type=script&lang=js&\"\nimport style0 from \"./responsive-layout.vue?vue&type=style&index=0&id=46ec3b4c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"46ec3b4c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/responsive-layout/responsive-layout.vue\"\nexport default component.exports", "export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=template&id=46ec3b4c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"responsive-layout\" :class=\"layoutClasses\" :style=\"layoutStyles\">\n    <slot></slot>\n  </view>\n</template>\n\n<script>\nimport multiPlatformAdapter from '@/common/js/pc-adapter.js'\n\nexport default {\n  name: 'ResponsiveLayout',\n  props: {\n    // 布局类型：container(容器), grid(栅格), flex(弹性), row(行), col(列)\n    type: {\n      type: String,\n      default: 'container',\n      validator: value => ['container', 'grid', 'flex', 'row', 'col'].includes(value)\n    },\n\n    // 响应式配置\n    responsive: {\n      type: Object,\n      default: () => ({})\n    },\n\n    // 最大宽度配置\n    maxWidth: {\n      type: [String, Number, Object],\n      default: null\n    },\n\n    // 内边距配置\n    padding: {\n      type: [String, Number, Object],\n      default: null\n    },\n\n    // 外边距配置\n    margin: {\n      type: [String, Number, Object],\n      default: null\n    },\n\n    // 栅格列数配置\n    cols: {\n      type: [Number, Object],\n      default: () => ({\n        mobile: 1,\n        tablet: 2,\n        desktop: 3,\n        large: 4\n      })\n    },\n\n    // 间距配置\n    gap: {\n      type: [String, Number, Object],\n      default: () => ({\n        mobile: '12px',\n        tablet: '16px',\n        desktop: '20px',\n        large: '24px'\n      })\n    },\n\n    // 对齐方式\n    align: {\n      type: [String, Object],\n      default: 'stretch'\n    },\n\n    // 水平对齐\n    justify: {\n      type: [String, Object],\n      default: 'start'\n    },\n\n    // 显示/隐藏控制\n    hidden: {\n      type: [String, Array],\n      default: null\n    },\n\n    visible: {\n      type: [String, Array],\n      default: null\n    },\n\n    // 是否启用PC端优化\n    pcOptimized: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      adapterInfo: {\n        platform: {},\n        device: {},\n        screenType: 'mobile'\n      }\n    }\n  },\n\n  computed: {\n    layoutClasses() {\n      const classes = ['responsive-layout', 'responsive-layout--' + this.type]\n\n      // 添加屏幕类型类\n      classes.push('responsive-layout--' + this.adapterInfo.device.screenType)\n\n      // 添加平台类\n      if (this.adapterInfo.platform.current) {\n        classes.push('responsive-layout--platform-' + this.adapterInfo.platform.current)\n      }\n\n      // 添加设备类型类\n      if (this.adapterInfo.platform.isPC) {\n        classes.push('responsive-layout--pc')\n      }\n\n      if (this.adapterInfo.device.isMobile) {\n        classes.push('responsive-layout--mobile-device')\n      }\n\n      // 添加对齐类\n      const align = this.align\n      if (align) {\n        classes.push('responsive-layout--align-' + align)\n      }\n\n      const justify = this.justify\n      if (justify) {\n        classes.push('responsive-layout--justify-' + justify)\n      }\n\n      // 处理显示/隐藏\n      if (this.shouldHide()) {\n        classes.push('responsive-layout--hidden')\n      }\n\n      // PC优化类\n      if (this.pcOptimized && this.adapterInfo.platform.isPC) {\n        classes.push('responsive-layout--pc-optimized')\n      }\n\n      return classes\n    },\n    \n    layoutStyles() {\n      const styles = {}\n\n      // 最大宽度\n      const maxWidth = this.maxWidth\n      if (maxWidth) {\n        styles.maxWidth = this.formatValue(maxWidth)\n      }\n\n      // 内边距\n      const padding = this.padding\n      if (padding) {\n        styles.padding = this.formatValue(padding)\n      }\n\n      // 外边距\n      const margin = this.margin\n      if (margin) {\n        styles.margin = this.formatValue(margin)\n      }\n\n      // 间距\n      const gap = this.gap\n      if (gap) {\n        styles.gap = this.formatValue(gap)\n      }\n\n      // 栅格布局\n      if (this.type === 'grid') {\n        const cols = this.cols\n        if (cols && cols > 0) {\n          styles.gridTemplateColumns = 'repeat(' + cols + ', 1fr)'\n        }\n      }\n\n      // 响应式配置\n      if (this.responsive) {\n        const responsiveStyles = this.responsive\n        if (responsiveStyles && typeof responsiveStyles === 'object') {\n          Object.assign(styles, responsiveStyles)\n        }\n      }\n\n      return styles\n    }\n  },\n\n  mounted() {\n    this.updateAdapterInfo()\n    this.setupEventListeners()\n  },\n\n  beforeDestroy() {\n    this.removeEventListeners()\n  },\n\n  methods: {\n    updateAdapterInfo() {\n      this.adapterInfo = multiPlatformAdapter.getAdapterInfo()\n    },\n\n    setupEventListeners() {\n      this.resizeHandler = () => {\n        this.updateAdapterInfo()\n      }\n\n      multiPlatformAdapter.on('resize', this.resizeHandler)\n    },\n\n    removeEventListeners() {\n      if (this.resizeHandler) {\n        multiPlatformAdapter.off('resize', this.resizeHandler)\n      }\n    },\n\n    getResponsiveValue(value) {\n      return multiPlatformAdapter.getResponsiveValue(value)\n    },\n\n    formatValue(value) {\n      if (typeof value === 'number') {\n        return value + 'px'\n      }\n      return value\n    },\n\n    shouldHide() {\n      const currentScreenType = this.adapterInfo.device.screenType\n\n      if (this.hidden) {\n        const hiddenTypes = Array.isArray(this.hidden) ? this.hidden : [this.hidden]\n        if (hiddenTypes.includes(currentScreenType)) {\n          return true\n        }\n      }\n\n      if (this.visible) {\n        const visibleTypes = Array.isArray(this.visible) ? this.visible : [this.visible]\n        if (!visibleTypes.includes(currentScreenType)) {\n          return true\n        }\n      }\n\n      return false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/common/css/pc-responsive.scss';\n\n.responsive-layout {\n  box-sizing: border-box;\n  transition: all 0.3s ease;\n\n  // 基础布局类型\n  &--container {\n    width: 100%;\n    margin: 0 auto;\n    @extend .responsive-container;\n  }\n\n  &--grid {\n    display: grid;\n    @extend .responsive-grid;\n  }\n\n  &--flex {\n    display: flex;\n    flex-wrap: wrap;\n  }\n\n  &--row {\n    display: flex;\n    flex-direction: row;\n    flex-wrap: wrap;\n  }\n\n  &--col {\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n    min-width: 0;\n  }\n\n  // 对齐方式\n  &--align-start { align-items: flex-start; }\n  &--align-center { align-items: center; }\n  &--align-end { align-items: flex-end; }\n  &--align-stretch { align-items: stretch; }\n\n  &--justify-start { justify-content: flex-start; }\n  &--justify-center { justify-content: center; }\n  &--justify-end { justify-content: flex-end; }\n  &--justify-between { justify-content: space-between; }\n  &--justify-around { justify-content: space-around; }\n  &--justify-evenly { justify-content: space-evenly; }\n\n  // 平台特定样式\n  &--platform-h5 {\n    // H5特定样式\n  }\n\n  &--platform-app {\n    // App特定样式\n  }\n\n  &--platform-mp-weixin {\n    // 微信小程序特定样式\n  }\n\n  // PC端优化\n  &--pc-optimized {\n    @include pc-only {\n      &.responsive-layout--container {\n        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n        border-radius: 8px;\n        background: #fff;\n      }\n\n      &:hover {\n        @extend .hover-lift;\n      }\n    }\n  }\n\n  // 屏幕类型样式\n  &--mobile {\n    // 移动端样式\n  }\n\n  &--tablet {\n    // 平板端样式\n  }\n\n  &--desktop {\n    // 桌面端样式\n  }\n\n  &--large {\n    // 大屏样式\n  }\n\n  // 隐藏控制\n  &--hidden {\n    display: none !important;\n  }\n}\n\n// 响应式容器最大宽度\n.responsive-layout--container {\n  @include respond-to(mobile) {\n    max-width: 100%;\n    padding: 0 16px;\n  }\n\n  @include respond-to(tablet) {\n    max-width: 750px;\n    padding: 0 24px;\n  }\n\n  @include respond-to(desktop) {\n    max-width: 1000px;\n    padding: 0 32px;\n  }\n\n  @include respond-to(large) {\n    max-width: 1200px;\n    padding: 0 40px;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=style&index=0&id=46ec3b4c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../uni-app/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./responsive-layout.vue?vue&type=style&index=0&id=46ec3b4c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753618714982\n      var cssReload = require(\"D:/uni-app/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}